# Open SWE MVP

A simplified but complete implementation of the Open SWE multi-agent system using LangGraph.

## Architecture

This MVP implements three core agents:

- **Manager Agent**: Handles message classification and agent coordination
- **Planner Agent**: Analyzes tasks and generates execution plans  
- **Programmer Agent**: Executes code changes and handles errors

## Features

- Multi-agent workflow with LangGraph
- Local code execution (no sandbox required)
- TypeScript with full type safety
- Extensible tool system
- Simple but complete agent coordination

## Setup

1. Install dependencies:
```bash
npm install
```

2. Copy environment file:
```bash
cp .env.example .env
```

3. Configure your API keys in `.env`

4. Build the project:
```bash
npm run build
```

## Usage

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### Testing
```bash
npm test
```

## Project Structure

```
src/
├── agents/          # Agent implementations
├── tools/           # Tool system
├── types/           # Type definitions
├── utils/           # Utility functions
└── index.ts         # Main entry point
```

## Example

```typescript
import { MultiAgentSystem } from './src/index.js';

const system = new MultiAgentSystem();
const result = await system.processRequest("Create a simple calculator function");
console.log(result);
```
