{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "./dist", "rootDir": "./src", "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "lib": ["ES2022", "DOM"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}