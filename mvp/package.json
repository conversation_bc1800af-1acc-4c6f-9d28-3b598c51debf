{"name": "open-swe-mvp", "version": "1.0.0", "description": "MVP implementation of Open SWE multi-agent system", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "test": "jest", "clean": "rm -rf dist"}, "dependencies": {"@langchain/core": "^0.3.65", "@langchain/langgraph": "^0.3.8", "@langchain/anthropic": "^0.3.26", "@langchain/openai": "^0.5.10", "zod": "^3.25.32", "uuid": "^11.0.5", "dotenv": "^16.4.7"}, "devDependencies": {"@types/node": "^22.13.5", "@types/uuid": "^11.0.0", "@types/jest": "^29.5.0", "typescript": "~5.7.2", "tsx": "^4.20.3", "jest": "^29.7.0", "ts-jest": "^29.1.0"}, "keywords": ["ai", "agent", "langgraph", "multi-agent", "coding"], "author": "Open SWE MVP", "license": "MIT"}