import dotenv from "dotenv";
import { MultiAgentCoordinator } from "./coordinator.js";
import { createLLMManager } from "./utils/index.js";
import { ProcessRequest } from "./types/index.js";

// Load environment variables
dotenv.config();

export class MultiAgentSystem {
  private coordinator: MultiAgentCoordinator;

  constructor() {
    const llmManager = createLLMManager();
    this.coordinator = new MultiAgentCoordinator(llmManager);
  }

  async processRequest(userInput: string, workingDirectory?: string): Promise<any> {
    const request: ProcessRequest = {
      userInput,
      workingDirectory: workingDirectory || process.env.WORK_DIR || "./workspace"
    };

    return await this.coordinator.processRequest(request);
  }

  async testConnection(): Promise<boolean> {
    try {
      const llmManager = createLLMManager();
      return await llmManager.testConnection();
    } catch (error) {
      console.error("Connection test failed:", error);
      return false;
    }
  }
}

// CLI interface for direct usage
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log("Usage: npm run dev \"<your request>\"");
    console.log("Example: npm run dev \"Create a simple calculator function\"");
    process.exit(1);
  }

  const userInput = args.join(" ");
  console.log(`Processing request: ${userInput}`);

  try {
    const system = new MultiAgentSystem();
    
    // Test connection first
    console.log("Testing LLM connection...");
    const connected = await system.testConnection();
    if (!connected) {
      console.error("Failed to connect to LLM. Please check your API keys in .env file.");
      process.exit(1);
    }
    console.log("✓ LLM connection successful");

    // Process the request
    console.log("\nStarting multi-agent processing...");
    const result = await system.processRequest(userInput);
    
    console.log("\n" + "=".repeat(50));
    console.log("RESULT:");
    console.log("=".repeat(50));
    console.log(`Success: ${result.success}`);
    console.log(`Task: ${result.task.title}`);
    console.log(`Status: ${result.task.status}`);
    console.log(`Execution Time: ${result.executionTime}ms`);
    console.log(`\nFinal Result:\n${result.finalResult}`);
    
    if (result.task.planItems.length > 0) {
      console.log(`\nPlan Items (${result.task.planItems.length}):`);
      result.task.planItems.forEach((item, index) => {
        console.log(`${index + 1}. [${item.status.toUpperCase()}] ${item.description}`);
      });
    }

    if (result.error) {
      console.log(`\nError: ${result.error}`);
    }

  } catch (error) {
    console.error("Fatal error:", error);
    process.exit(1);
  }
}

// Export for library usage
export { MultiAgentCoordinator } from "./coordinator.js";
export * from "./types/index.js";
export * from "./agents/index.js";
export * from "./tools/index.js";
export * from "./utils/index.js";

// Run CLI if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
