import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import { MultiAgentSystem } from '../index.js';
import { AgentType } from '../types/index.js';
import { createLLMManager } from '../utils/index.js';
import { readFile, writeFile, mkdir } from 'fs/promises';
import { join } from 'path';

describe('MultiAgentSystem', () => {
  let system: MultiAgentSystem;
  let testWorkspace: string;

  beforeAll(async () => {
    // Setup test environment
    testWorkspace = join(process.cwd(), 'test-workspace');
    await mkdir(testWorkspace, { recursive: true });
    
    // Set test environment variables
    process.env.DEFAULT_MODEL_PROVIDER = 'anthropic';
    process.env.DEFAULT_MODEL_NAME = 'claude-3-5-sonnet-20241022';
    process.env.WORK_DIR = testWorkspace;
    process.env.DEBUG = 'true';
    
    system = new MultiAgentSystem();
  });

  afterAll(async () => {
    // Cleanup test workspace if needed
    // Note: Keeping files for inspection in MVP
  });

  test('should initialize successfully', () => {
    expect(system).toBeDefined();
    expect(system).toBeInstanceOf(MultiAgentSystem);
  });

  test('should test LLM connection', async () => {
    // Skip if no API key is provided
    if (!process.env.ANTHROPIC_API_KEY && !process.env.OPENAI_API_KEY) {
      console.log('Skipping LLM connection test - no API key provided');
      return;
    }

    const connected = await system.testConnection();
    expect(connected).toBe(true);
  }, 30000);

  test('should process simple request', async () => {
    // Skip if no API key is provided
    if (!process.env.ANTHROPIC_API_KEY && !process.env.OPENAI_API_KEY) {
      console.log('Skipping request processing test - no API key provided');
      return;
    }

    const result = await system.processRequest(
      "Create a simple hello world function",
      testWorkspace
    );

    expect(result).toBeDefined();
    expect(result.success).toBeDefined();
    expect(result.task).toBeDefined();
    expect(result.task.title).toBeDefined();
    expect(result.finalResult).toBeDefined();
    expect(result.executionTime).toBeGreaterThan(0);
  }, 60000);

  test('should handle file creation request', async () => {
    // Skip if no API key is provided
    if (!process.env.ANTHROPIC_API_KEY && !process.env.OPENAI_API_KEY) {
      console.log('Skipping file creation test - no API key provided');
      return;
    }

    const result = await system.processRequest(
      "Create a README.md file with project description",
      testWorkspace
    );

    expect(result).toBeDefined();
    expect(result.task).toBeDefined();
    
    // Check if task has plan items
    if (result.task.planItems) {
      expect(result.task.planItems.length).toBeGreaterThan(0);
    }
  }, 60000);
});

describe('LLMManager', () => {
  test('should create LLM manager with default config', () => {
    const llmManager = createLLMManager();
    expect(llmManager).toBeDefined();
  });

  test('should get model instance', () => {
    const llmManager = createLLMManager();
    const model = llmManager.getModel();
    expect(model).toBeDefined();
  });
});

describe('Tools', () => {
  test('should import all tools successfully', async () => {
    const { allTools, toolRegistry } = await import('../tools/index.js');
    
    expect(allTools).toBeDefined();
    expect(Array.isArray(allTools)).toBe(true);
    expect(allTools.length).toBeGreaterThan(0);
    
    expect(toolRegistry).toBeDefined();
    expect(typeof toolRegistry).toBe('object');
    expect(Object.keys(toolRegistry).length).toBeGreaterThan(0);
  });

  test('should execute shell tool', async () => {
    const { shellTool } = await import('../tools/shell.js');
    
    const result = await shellTool.invoke({
      command: 'echo "test"',
      timeout: 5
    });

    expect(result).toBeDefined();
    expect(typeof result).toBe('object');
    
    if (typeof result === 'object' && 'success' in result) {
      expect(result.success).toBe(true);
      expect(result.result).toContain('test');
    }
  });

  test('should execute file operations', async () => {
    const { writeFileTool, readFileTool } = await import('../tools/file-operations.js');
    
    const testFile = join(testWorkspace, 'test.txt');
    const testContent = 'Hello, World!';
    
    // Write file
    const writeResult = await writeFileTool.invoke({
      filePath: testFile,
      content: testContent
    });
    
    expect(writeResult).toBeDefined();
    if (typeof writeResult === 'object' && 'success' in writeResult) {
      expect(writeResult.success).toBe(true);
    }
    
    // Read file
    const readResult = await readFileTool.invoke({
      filePath: testFile
    });
    
    expect(readResult).toBeDefined();
    if (typeof readResult === 'object' && 'success' in readResult) {
      expect(readResult.success).toBe(true);
      expect(readResult.result).toBe(testContent);
    }
  });
});

describe('Types and Utilities', () => {
  test('should import types successfully', async () => {
    const types = await import('../types/index.js');
    
    expect(types.AgentType).toBeDefined();
    expect(types.AgentType.MANAGER).toBe('manager');
    expect(types.AgentType.PLANNER).toBe('planner');
    expect(types.AgentType.PROGRAMMER).toBe('programmer');
  });

  test('should create task and plan items', async () => {
    const { createTask, createPlanItem } = await import('../utils/index.js');
    
    const task = createTask('Test Task', 'Test Description');
    expect(task).toBeDefined();
    expect(task.id).toBeDefined();
    expect(task.title).toBe('Test Task');
    expect(task.description).toBe('Test Description');
    expect(task.status).toBe('pending');
    
    const planItem = createPlanItem('Test Plan Item');
    expect(planItem).toBeDefined();
    expect(planItem.id).toBeDefined();
    expect(planItem.description).toBe('Test Plan Item');
    expect(planItem.status).toBe('pending');
  });
});
