import { AgentType, GraphState, AgentResponse, Task, BaseMessage } from "../types/simple.js";
import { log, createError, createTask, createMessage, generateMockResponse, delay } from "../utils/simple.js";

interface Classification {
  intent: "code_task" | "question" | "clarification" | "help";
  confidence: number;
  reasoning: string;
  nextAgent: AgentType;
  requiresPlanning: boolean;
}

export class SimpleManagerAgent {
  constructor(private mockMode = true) {}

  async processMessage(state: GraphState, userMessage: string): Promise<AgentResponse> {
    try {
      log(AgentType.MANAGER, "Processing user message", { message: userMessage });

      // Simulate thinking time
      await delay(500);

      // Classify the message
      const classification = this.classifyMessage(userMessage, state);
      
      // Create or update task based on classification
      const task = this.handleClassification(classification, userMessage, state);
      
      // Determine next steps
      const nextAgent = this.determineNextAgent(classification, task);
      
      // Generate response message
      const responseMessage = this.generateResponseMessage(classification, task);

      return {
        agentType: AgentType.MANAGER,
        action: "message_classified",
        result: {
          classification,
          task,
          message: responseMessage
        },
        nextAgent,
        shouldContinue: nextAgent !== AgentType.MANAGER
      };
    } catch (error) {
      log(AgentType.MANAGER, "Error processing message", { error: error.message });
      throw createError(AgentType.MANAGER, "Failed to process message", error as Error);
    }
  }

  private classifyMessage(message: string, state: GraphState): Classification {
    const lowerMessage = message.toLowerCase();
    
    // Simple rule-based classification
    let intent: Classification["intent"] = "question";
    let nextAgent: AgentType = AgentType.MANAGER;
    let requiresPlanning = false;

    if (lowerMessage.includes("create") || 
        lowerMessage.includes("build") || 
        lowerMessage.includes("implement") ||
        lowerMessage.includes("write") ||
        lowerMessage.includes("code") ||
        lowerMessage.includes("function") ||
        lowerMessage.includes("file")) {
      intent = "code_task";
      nextAgent = AgentType.PLANNER;
      requiresPlanning = true;
    } else if (lowerMessage.includes("help") || 
               lowerMessage.includes("how") ||
               lowerMessage.includes("what")) {
      intent = "help";
      nextAgent = AgentType.MANAGER;
    } else if (state.currentTask && 
               (lowerMessage.includes("yes") || 
                lowerMessage.includes("no") || 
                lowerMessage.includes("change") ||
                lowerMessage.includes("modify"))) {
      intent = "clarification";
      nextAgent = state.currentTask.status === "pending" ? AgentType.PLANNER : AgentType.PROGRAMMER;
    }

    return {
      intent,
      confidence: 0.8,
      reasoning: `Classified as ${intent} based on keyword analysis`,
      nextAgent,
      requiresPlanning
    };
  }

  private handleClassification(
    classification: Classification,
    userMessage: string,
    state: GraphState
  ): Task {
    switch (classification.intent) {
      case "code_task":
        // Create new task for code-related requests
        return createTask(
          `Code Task: ${userMessage.substring(0, 50)}${userMessage.length > 50 ? '...' : ''}`,
          userMessage
        );
      
      case "clarification":
        // Update existing task if available
        if (state.currentTask) {
          return {
            ...state.currentTask,
            description: `${state.currentTask.description}\n\nUser clarification: ${userMessage}`,
            updatedAt: Date.now()
          };
        }
        // Fall through to create new task if no current task
        
      case "question":
      case "help":
      default:
        // For questions and help, create a simple task or return existing
        return state.currentTask || createTask(
          "Information Request",
          userMessage
        );
    }
  }

  private determineNextAgent(classification: Classification, task: Task): AgentType {
    if (classification.intent === "help" || classification.intent === "question") {
      return AgentType.MANAGER;
    }
    
    if (classification.requiresPlanning || task.planItems.length === 0) {
      return AgentType.PLANNER;
    }
    
    return AgentType.PROGRAMMER;
  }

  private generateResponseMessage(classification: Classification, task: Task): string {
    const mockResponse = generateMockResponse(AgentType.MANAGER, "");
    
    switch (classification.intent) {
      case "code_task":
        return `${mockResponse} I'll ${classification.requiresPlanning ? "start by planning the approach" : "begin implementation"}.`;
      
      case "question":
        return `${mockResponse} Let me analyze what you're asking about.`;
      
      case "clarification":
        return `${mockResponse} I'll update the current task accordingly.`;
      
      case "help":
        return `${mockResponse} I can assist with coding tasks, answer questions, and guide you through the development process.`;
      
      default:
        return `${mockResponse} I've received your message and will process it accordingly.`;
    }
  }

  async handleHelp(): Promise<AgentResponse> {
    const helpMessage = `
I'm a multi-agent system that can help you with coding tasks. Here's what I can do:

🎯 **Manager Agent** (me): Routes requests and coordinates other agents
📋 **Planner Agent**: Analyzes tasks and creates detailed execution plans  
💻 **Programmer Agent**: Executes code changes and handles implementation

**Example requests:**
- "Create a simple calculator function"
- "Write a README file for this project"
- "Build a basic web server"
- "Implement a sorting algorithm"

Just tell me what you'd like to build or implement!
    `;

    return {
      agentType: AgentType.MANAGER,
      action: "help_provided",
      result: {
        message: helpMessage.trim()
      },
      shouldContinue: false
    };
  }
}
