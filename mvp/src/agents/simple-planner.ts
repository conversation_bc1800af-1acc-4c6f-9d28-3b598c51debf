import { AgentType, GraphState, AgentResponse, Task, PlanItem } from "../types/simple.js";
import { log, createError, createPlanItem, updateTaskStatus, generateMockResponse, delay } from "../utils/simple.js";
import { toolRegistry } from "../tools/simple.js";

interface Plan {
  title: string;
  summary: string;
  steps: Array<{
    description: string;
    type: "analysis" | "implementation" | "testing" | "documentation";
    estimatedTime?: number;
    dependencies?: string[];
    tools?: string[];
  }>;
  risks?: string[];
  requirements?: string[];
}

export class SimplePlannerAgent {
  constructor(private mockMode = true) {}

  async createPlan(state: GraphState, task: Task): Promise<AgentResponse> {
    try {
      log(AgentType.PLANNER, "Creating plan for task", { taskId: task.id, title: task.title });

      // Simulate thinking time
      await delay(1000);

      // Gather context about the working environment
      const context = await this.gatherContext(state);
      
      // Generate the plan
      const plan = this.generatePlan(task, context);
      
      // Convert plan to plan items
      const planItems = this.convertPlanToPlanItems(plan);
      
      // Update task with plan
      const updatedTask: Task = {
        ...task,
        planItems,
        status: "in_progress",
        updatedAt: Date.now()
      };

      const responseMessage = `${generateMockResponse(AgentType.PLANNER, "")}

**Plan for: ${task.title}**

${this.formatPlanSummary(plan)}

I've created a plan with ${planItems.length} steps. Ready to begin execution!`;

      return {
        agentType: AgentType.PLANNER,
        action: "plan_created",
        result: {
          plan,
          task: updatedTask,
          message: responseMessage
        },
        nextAgent: AgentType.PROGRAMMER,
        shouldContinue: true
      };
    } catch (error) {
      log(AgentType.PLANNER, "Error creating plan", { error: error.message });
      throw createError(AgentType.PLANNER, "Failed to create plan", error as Error);
    }
  }

  async refinePlan(state: GraphState, task: Task, feedback: string): Promise<AgentResponse> {
    try {
      log(AgentType.PLANNER, "Refining plan based on feedback", { taskId: task.id, feedback });

      // Simulate thinking time
      await delay(800);

      // Analyze current plan and feedback
      const refinedPlan = this.generateRefinedPlan(task, feedback);
      
      // Convert to plan items
      const planItems = this.convertPlanToPlanItems(refinedPlan);
      
      // Update task
      const updatedTask: Task = {
        ...task,
        planItems,
        updatedAt: Date.now()
      };

      const responseMessage = `${generateMockResponse(AgentType.PLANNER, "")}

**Refined Plan for: ${task.title}**

Based on your feedback: "${feedback}"

${this.formatPlanSummary(refinedPlan)}`;

      return {
        agentType: AgentType.PLANNER,
        action: "plan_refined",
        result: {
          plan: refinedPlan,
          task: updatedTask,
          message: responseMessage
        },
        nextAgent: AgentType.PROGRAMMER,
        shouldContinue: true
      };
    } catch (error) {
      log(AgentType.PLANNER, "Error refining plan", { error: error.message });
      throw createError(AgentType.PLANNER, "Failed to refine plan", error as Error);
    }
  }

  private async gatherContext(state: GraphState): Promise<Record<string, any>> {
    const context: Record<string, any> = {
      workingDirectory: state.workingDirectory,
      availableTools: Object.keys(toolRegistry),
      recentHistory: state.agentHistory.slice(-5)
    };

    try {
      // Try to get directory listing to understand the project structure
      const listResult = await toolRegistry.list_directory(state.workingDirectory);
      if (listResult.success) {
        context.projectStructure = JSON.parse(listResult.result);
      }
    } catch (error) {
      log(AgentType.PLANNER, "Could not gather full context", { error: error.message });
      // Continue without full context
    }

    return context;
  }

  private generatePlan(task: Task, context: Record<string, any>): Plan {
    const description = task.description.toLowerCase();
    const steps: Plan["steps"] = [];

    // Generate steps based on task description
    if (description.includes("create") || description.includes("build")) {
      if (description.includes("function") || description.includes("calculator")) {
        steps.push(
          {
            description: "Analyze requirements and design function interface",
            type: "analysis",
            estimatedTime: 5,
            tools: ["read_file", "list_directory"]
          },
          {
            description: "Implement the main function logic",
            type: "implementation", 
            estimatedTime: 15,
            tools: ["write_file"],
            dependencies: ["step-1"]
          },
          {
            description: "Create test cases and verify functionality",
            type: "testing",
            estimatedTime: 10,
            tools: ["write_file", "shell"],
            dependencies: ["step-2"]
          }
        );
      } else if (description.includes("readme") || description.includes("documentation")) {
        steps.push(
          {
            description: "Analyze project structure and gather information",
            type: "analysis",
            estimatedTime: 5,
            tools: ["list_directory", "read_file"]
          },
          {
            description: "Write comprehensive README documentation",
            type: "documentation",
            estimatedTime: 15,
            tools: ["write_file"],
            dependencies: ["step-1"]
          }
        );
      } else if (description.includes("server") || description.includes("web")) {
        steps.push(
          {
            description: "Design server architecture and endpoints",
            type: "analysis",
            estimatedTime: 10,
            tools: ["write_file"]
          },
          {
            description: "Implement basic server setup",
            type: "implementation",
            estimatedTime: 20,
            tools: ["write_file"],
            dependencies: ["step-1"]
          },
          {
            description: "Add routing and request handling",
            type: "implementation",
            estimatedTime: 15,
            tools: ["write_file"],
            dependencies: ["step-2"]
          },
          {
            description: "Test server functionality",
            type: "testing",
            estimatedTime: 10,
            tools: ["shell"],
            dependencies: ["step-3"]
          }
        );
      } else {
        // Generic implementation plan
        steps.push(
          {
            description: "Analyze requirements and plan implementation",
            type: "analysis",
            estimatedTime: 10,
            tools: ["list_directory", "read_file"]
          },
          {
            description: "Implement the requested functionality",
            type: "implementation",
            estimatedTime: 20,
            tools: ["write_file", "shell"],
            dependencies: ["step-1"]
          },
          {
            description: "Test and verify the implementation",
            type: "testing",
            estimatedTime: 10,
            tools: ["shell"],
            dependencies: ["step-2"]
          }
        );
      }
    } else {
      // Default plan for other types of requests
      steps.push({
        description: "Complete the requested task",
        type: "implementation",
        estimatedTime: 15,
        tools: ["shell", "write_file"]
      });
    }

    return {
      title: task.title,
      summary: `Plan to complete: ${task.description}`,
      steps,
      risks: ["Unexpected errors during execution", "Missing dependencies"],
      requirements: ["Access to working directory", "Required tools available"]
    };
  }

  private generateRefinedPlan(task: Task, feedback: string): Plan {
    // For simplicity, generate a new plan with feedback consideration
    const originalPlan = this.generatePlan(task, {});
    
    // Add a step to address the feedback
    const refinedSteps = [
      {
        description: `Address feedback: ${feedback}`,
        type: "analysis" as const,
        estimatedTime: 5,
        tools: ["read_file", "write_file"]
      },
      ...originalPlan.steps
    ];

    return {
      ...originalPlan,
      summary: `Refined plan to complete: ${task.description} (addressing: ${feedback})`,
      steps: refinedSteps
    };
  }

  private convertPlanToPlanItems(plan: Plan): PlanItem[] {
    return plan.steps.map((step, index) => {
      const stepId = `step-${index + 1}`;
      return createPlanItem(
        step.description,
        step.dependencies?.map(dep => dep === `step-${index}` ? `step-${index}` : dep)
      );
    });
  }

  private formatPlanSummary(plan: Plan): string {
    return plan.steps.map((step, index) => 
      `${index + 1}. **${step.type.toUpperCase()}**: ${step.description} (~${step.estimatedTime || 10}min)`
    ).join('\n');
  }
}
