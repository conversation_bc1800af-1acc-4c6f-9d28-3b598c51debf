import { BaseMessage } from "@langchain/core/messages";
import { AgentType, GraphState, AgentResponse, Task, PlanItem, ToolResult } from "../types/index.js";
import { LLMManager, log, createError, updatePlanItemStatus, getNextPlanItem, isTaskComplete, hasTaskFailed } from "../utils/index.js";
import { allTools, toolRegistry } from "../tools/index.js";

export class ProgrammerAgent {
  private maxRetries = 3;

  constructor(private llmManager: LLMManager) {}

  async executeTask(state: GraphState, task: Task): Promise<AgentResponse> {
    try {
      log(AgentType.PROGRAMMER, "Starting task execution", { taskId: task.id, title: task.title });

      // Get the next plan item to execute
      const nextPlanItem = getNextPlanItem(task);
      
      if (!nextPlanItem) {
        if (isTaskComplete(task)) {
          return this.handleTaskCompletion(task);
        } else if (hasTaskFailed(task)) {
          return this.handleTaskFailure(task);
        } else {
          throw new Error("No available plan items to execute");
        }
      }

      // Execute the plan item
      const result = await this.executePlanItem(state, task, nextPlanItem);
      
      return result;
    } catch (error) {
      log(AgentType.PROGRAMMER, "Error executing task", { error: error.message });
      throw createError(AgentType.PROGRAMMER, "Failed to execute task", error as Error);
    }
  }

  async handleError(state: GraphState, task: Task, error: string): Promise<AgentResponse> {
    try {
      log(AgentType.PROGRAMMER, "Handling error", { taskId: task.id, error });

      // Analyze the error and determine recovery strategy
      const recovery = await this.analyzeError(error, state, task);
      
      if (recovery.canRecover) {
        // Try to recover by executing the recovery action
        const recoveryResult = await this.executeRecoveryAction(state, recovery.action);
        
        if (recoveryResult.success) {
          return {
            agentType: AgentType.PROGRAMMER,
            action: "error_recovered",
            result: {
              message: `Recovered from error: ${recovery.explanation}`,
              recoveryAction: recovery.action
            },
            nextAgent: AgentType.PROGRAMMER,
            shouldContinue: true
          };
        }
      }

      // If recovery failed or not possible, escalate to planner
      return {
        agentType: AgentType.PROGRAMMER,
        action: "error_escalated",
        result: {
          error,
          message: "Unable to recover from error. Requesting plan revision.",
          analysis: recovery.explanation
        },
        nextAgent: AgentType.PLANNER,
        shouldContinue: true
      };
    } catch (recoveryError) {
      log(AgentType.PROGRAMMER, "Error in error handling", { error: recoveryError.message });
      throw createError(AgentType.PROGRAMMER, "Failed to handle error", recoveryError as Error);
    }
  }

  private async executePlanItem(state: GraphState, task: Task, planItem: PlanItem): Promise<AgentResponse> {
    log(AgentType.PROGRAMMER, "Executing plan item", { planItemId: planItem.id, description: planItem.description });

    // Mark plan item as in progress
    const updatedTask = updatePlanItemStatus(task, planItem.id, "in_progress");

    try {
      // Generate action plan for this specific plan item
      const actionPlan = await this.generateActionPlan(state, planItem);
      
      // Execute the actions
      const executionResult = await this.executeActions(state, actionPlan);
      
      if (executionResult.success) {
        // Mark plan item as completed
        const completedTask = updatePlanItemStatus(updatedTask, planItem.id, "completed");
        
        // Check if task is complete
        if (isTaskComplete(completedTask)) {
          return this.handleTaskCompletion(completedTask);
        }
        
        return {
          agentType: AgentType.PROGRAMMER,
          action: "plan_item_completed",
          result: {
            task: completedTask,
            planItem,
            executionResult,
            message: `Completed: ${planItem.description}`
          },
          nextAgent: AgentType.PROGRAMMER,
          shouldContinue: true
        };
      } else {
        // Mark plan item as failed
        const failedTask = updatePlanItemStatus(updatedTask, planItem.id, "failed", executionResult.error);
        
        return {
          agentType: AgentType.PROGRAMMER,
          action: "plan_item_failed",
          result: {
            task: failedTask,
            planItem,
            error: executionResult.error,
            message: `Failed to complete: ${planItem.description}`
          },
          nextAgent: AgentType.PROGRAMMER,
          shouldContinue: true,
          error: executionResult.error
        };
      }
    } catch (error) {
      // Mark plan item as failed
      const failedTask = updatePlanItemStatus(updatedTask, planItem.id, "failed", error.message);
      
      return {
        agentType: AgentType.PROGRAMMER,
        action: "plan_item_failed",
        result: {
          task: failedTask,
          planItem,
          error: error.message,
          message: `Failed to complete: ${planItem.description}`
        },
        nextAgent: AgentType.PROGRAMMER,
        shouldContinue: true,
        error: error.message
      };
    }
  }

  private async generateActionPlan(state: GraphState, planItem: PlanItem): Promise<Array<{
    tool: string;
    args: Record<string, any>;
    description: string;
  }>> {
    const model = this.llmManager.getModel("programmer");
    const toolModel = model.bindTools(allTools);

    const systemPrompt = `You are a programmer agent executing a specific plan item. 
    
Plan item: ${planItem.description}
Working directory: ${state.workingDirectory}
Available tools: ${Object.keys(toolRegistry).join(", ")}

Generate the specific actions needed to complete this plan item. Use the available tools to:
1. Analyze the current state
2. Implement the required changes
3. Verify the results

Be specific and methodical in your approach.`;

    const response = await toolModel.invoke([
      { role: "system", content: systemPrompt },
      { role: "user", content: `Execute this plan item: ${planItem.description}` }
    ]);

    // Extract tool calls from the response
    const actions: Array<{ tool: string; args: Record<string, any>; description: string }> = [];
    
    if (response.tool_calls && response.tool_calls.length > 0) {
      for (const toolCall of response.tool_calls) {
        actions.push({
          tool: toolCall.name,
          args: toolCall.args,
          description: `Using ${toolCall.name} to ${JSON.stringify(toolCall.args)}`
        });
      }
    } else {
      // If no tool calls, create a default action based on the plan item
      actions.push({
        tool: "shell",
        args: { command: `echo "Executing: ${planItem.description}"` },
        description: `Default action for: ${planItem.description}`
      });
    }

    return actions;
  }

  private async executeActions(state: GraphState, actions: Array<{
    tool: string;
    args: Record<string, any>;
    description: string;
  }>): Promise<ToolResult> {
    const results: string[] = [];
    
    for (const action of actions) {
      log(AgentType.PROGRAMMER, "Executing action", { tool: action.tool, description: action.description });
      
      try {
        const tool = toolRegistry[action.tool as keyof typeof toolRegistry];
        if (!tool) {
          throw new Error(`Unknown tool: ${action.tool}`);
        }

        const result = await tool.invoke(action.args);
        
        if (typeof result === 'object' && 'success' in result) {
          const toolResult = result as ToolResult;
          if (toolResult.success) {
            results.push(`✓ ${action.description}: ${toolResult.result}`);
          } else {
            return {
              success: false,
              result: results.join('\n'),
              error: `Action failed: ${toolResult.error}`,
              metadata: { failedAction: action }
            };
          }
        } else {
          results.push(`✓ ${action.description}: ${result}`);
        }
      } catch (error) {
        return {
          success: false,
          result: results.join('\n'),
          error: `Action failed: ${error.message}`,
          metadata: { failedAction: action }
        };
      }
    }

    return {
      success: true,
      result: results.join('\n'),
      metadata: { actionsExecuted: actions.length }
    };
  }

  private async analyzeError(error: string, state: GraphState, task: Task): Promise<{
    canRecover: boolean;
    action?: string;
    explanation: string;
  }> {
    const model = this.llmManager.getModel("programmer");

    const systemPrompt = `You are analyzing an error to determine if it can be recovered from automatically.

Error: ${error}
Current task: ${task.title}
Working directory: ${state.workingDirectory}

Determine if this error can be automatically recovered from and suggest a recovery action if possible.
Common recoverable errors include:
- Missing files or directories
- Permission issues
- Simple syntax errors
- Missing dependencies

Provide a clear explanation and recovery strategy.`;

    const response = await model.invoke([
      { role: "system", content: systemPrompt },
      { role: "user", content: "Analyze this error and suggest recovery if possible." }
    ]);

    const content = response.content.toString().toLowerCase();
    
    // Simple recovery analysis - in a real implementation, this would be more sophisticated
    const canRecover = content.includes("can recover") || content.includes("recoverable") || 
                      content.includes("fix") || content.includes("create");
    
    let action = "";
    if (canRecover) {
      if (content.includes("create") || content.includes("mkdir")) {
        action = "create_missing_directory";
      } else if (content.includes("install") || content.includes("dependency")) {
        action = "install_dependencies";
      } else {
        action = "retry_with_fix";
      }
    }

    return {
      canRecover,
      action: canRecover ? action : undefined,
      explanation: response.content.toString()
    };
  }

  private async executeRecoveryAction(state: GraphState, action: string): Promise<ToolResult> {
    switch (action) {
      case "create_missing_directory":
        return await toolRegistry.shell.invoke({
          command: `mkdir -p ${state.workingDirectory}`,
          workingDirectory: state.workingDirectory
        });
      
      case "install_dependencies":
        return await toolRegistry.shell.invoke({
          command: "npm install",
          workingDirectory: state.workingDirectory
        });
      
      default:
        return {
          success: false,
          result: "",
          error: `Unknown recovery action: ${action}`
        };
    }
  }

  private handleTaskCompletion(task: Task): AgentResponse {
    return {
      agentType: AgentType.PROGRAMMER,
      action: "task_completed",
      result: {
        task,
        message: `Task completed successfully: ${task.title}`,
        summary: `Completed ${task.planItems.length} plan items`
      },
      nextAgent: AgentType.MANAGER,
      shouldContinue: false
    };
  }

  private handleTaskFailure(task: Task): AgentResponse {
    const failedItems = task.planItems.filter(item => item.status === "failed");
    
    return {
      agentType: AgentType.PROGRAMMER,
      action: "task_failed",
      result: {
        task,
        message: `Task failed: ${task.title}`,
        failedItems,
        summary: `${failedItems.length} plan items failed`
      },
      nextAgent: AgentType.PLANNER,
      shouldContinue: true,
      error: `Task failed with ${failedItems.length} failed plan items`
    };
  }
}
