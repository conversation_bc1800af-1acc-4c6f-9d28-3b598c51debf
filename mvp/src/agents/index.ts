export { ManagerAgent } from "./manager.js";
export { PlannerAgent } from "./planner.js";
export { ProgrammerAgent } from "./programmer.js";

import { ManagerAgent } from "./manager.js";
import { PlannerAgent } from "./planner.js";
import { ProgrammerAgent } from "./programmer.js";

export const agentRegistry = {
  manager: ManagerAgent,
  planner: PlannerAgent,
  programmer: ProgrammerAgent
};

export type AgentRegistry = typeof agentRegistry;
