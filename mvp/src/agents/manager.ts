import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { z } from "zod";
import { AgentType, GraphState, AgentResponse, Task } from "../types/index.js";
import { LLMManager, log, createError, createTask } from "../utils/index.js";

const ClassificationSchema = z.object({
  intent: z.enum(["code_task", "question", "clarification", "help"]).describe("The intent of the user message"),
  confidence: z.number().min(0).max(1).describe("Confidence in the classification"),
  reasoning: z.string().describe("Reasoning for the classification"),
  nextAgent: z.enum(["planner", "programmer", "manager"]).describe("Which agent should handle this next"),
  requiresPlanning: z.boolean().describe("Whether this task requires planning before execution")
});

export class ManagerAgent {
  constructor(private llmManager: LLMManager) {}

  async processMessage(state: GraphState, userMessage: string): Promise<AgentResponse> {
    try {
      log(AgentType.MANAGER, "Processing user message", { message: userMessage });

      // Classify the message
      const classification = await this.classifyMessage(userMessage, state);
      
      // Create or update task based on classification
      const task = await this.handleClassification(classification, userMessage, state);
      
      // Determine next steps
      const nextAgent = this.determineNextAgent(classification, task);
      
      return {
        agentType: AgentType.MANAGER,
        action: "message_classified",
        result: {
          classification,
          task,
          message: this.generateResponseMessage(classification, task)
        },
        nextAgent,
        shouldContinue: nextAgent !== AgentType.MANAGER
      };
    } catch (error) {
      log(AgentType.MANAGER, "Error processing message", { error: error.message });
      throw createError(AgentType.MANAGER, "Failed to process message", error as Error);
    }
  }

  private async classifyMessage(message: string, state: GraphState): Promise<z.infer<typeof ClassificationSchema>> {
    const model = this.llmManager.getModel("manager");
    
    const systemPrompt = `You are a manager agent responsible for classifying user messages and routing them to appropriate agents.

Current context:
- Working directory: ${state.workingDirectory}
- Current task: ${state.currentTask ? state.currentTask.title : "None"}
- Recent agent history: ${JSON.stringify(state.agentHistory.slice(-3), null, 2)}

Classify the user message according to these categories:
- code_task: User wants to create, modify, or analyze code
- question: User is asking for information or explanation
- clarification: User is providing clarification or feedback on current task
- help: User needs help or guidance

Determine which agent should handle this:
- planner: For complex tasks that need planning and analysis
- programmer: For direct code execution or simple modifications
- manager: For questions, help, or when more clarification is needed`;

    const response = await model.invoke([
      { role: "system", content: systemPrompt },
      { role: "user", content: `Please classify this message: "${message}"` }
    ]);

    // Parse the response to extract classification
    // For simplicity, we'll use a basic parsing approach
    const content = response.content.toString();
    
    // This is a simplified classification - in a real implementation,
    // you'd want to use structured output or tool calling
    let intent: "code_task" | "question" | "clarification" | "help" = "question";
    let nextAgent: "planner" | "programmer" | "manager" = "manager";
    let requiresPlanning = false;

    if (content.toLowerCase().includes("code") || content.toLowerCase().includes("create") || content.toLowerCase().includes("implement")) {
      intent = "code_task";
      nextAgent = "planner";
      requiresPlanning = true;
    } else if (content.toLowerCase().includes("help")) {
      intent = "help";
      nextAgent = "manager";
    } else if (state.currentTask && (content.toLowerCase().includes("yes") || content.toLowerCase().includes("no") || content.toLowerCase().includes("change"))) {
      intent = "clarification";
      nextAgent = state.currentTask.status === "pending" ? "planner" : "programmer";
    }

    return {
      intent,
      confidence: 0.8,
      reasoning: `Classified as ${intent} based on content analysis`,
      nextAgent,
      requiresPlanning
    };
  }

  private async handleClassification(
    classification: z.infer<typeof ClassificationSchema>,
    userMessage: string,
    state: GraphState
  ): Promise<Task> {
    switch (classification.intent) {
      case "code_task":
        // Create new task for code-related requests
        return createTask(
          `Code Task: ${userMessage.substring(0, 50)}...`,
          userMessage
        );
      
      case "clarification":
        // Update existing task if available
        if (state.currentTask) {
          return {
            ...state.currentTask,
            description: `${state.currentTask.description}\n\nUser clarification: ${userMessage}`,
            updatedAt: Date.now()
          };
        }
        // Fall through to create new task if no current task
        
      case "question":
      case "help":
      default:
        // For questions and help, create a simple task or return existing
        return state.currentTask || createTask(
          "Information Request",
          userMessage
        );
    }
  }

  private determineNextAgent(
    classification: z.infer<typeof ClassificationSchema>,
    task: Task
  ): AgentType {
    if (classification.intent === "help" || classification.intent === "question") {
      return AgentType.MANAGER;
    }
    
    if (classification.requiresPlanning || task.planItems.length === 0) {
      return AgentType.PLANNER;
    }
    
    return AgentType.PROGRAMMER;
  }

  private generateResponseMessage(
    classification: z.infer<typeof ClassificationSchema>,
    task: Task
  ): string {
    switch (classification.intent) {
      case "code_task":
        return `I understand you want to work on a coding task. I'll ${classification.requiresPlanning ? "start by planning the approach" : "begin implementation"}.`;
      
      case "question":
        return "I'll help answer your question. Let me analyze what you're asking about.";
      
      case "clarification":
        return "Thank you for the clarification. I'll update the current task accordingly.";
      
      case "help":
        return "I'm here to help! I can assist with coding tasks, answer questions, and guide you through the development process.";
      
      default:
        return "I've received your message and will process it accordingly.";
    }
  }
}
