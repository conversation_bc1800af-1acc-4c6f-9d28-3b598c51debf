import { BaseMessage } from "@langchain/core/messages";
import { z } from "zod";
import { AgentType, GraphState, AgentResponse, Task, PlanItem } from "../types/index.js";
import { LLMManager, log, createError, createPlanItem, updateTaskStatus } from "../utils/index.js";
import { allTools } from "../tools/index.js";

const PlanSchema = z.object({
  title: z.string().describe("A clear title for the plan"),
  summary: z.string().describe("A brief summary of what will be accomplished"),
  steps: z.array(z.object({
    description: z.string().describe("What needs to be done in this step"),
    type: z.enum(["analysis", "implementation", "testing", "documentation"]).describe("Type of step"),
    estimatedTime: z.number().optional().describe("Estimated time in minutes"),
    dependencies: z.array(z.string()).optional().describe("IDs of steps this depends on"),
    tools: z.array(z.string()).optional().describe("Tools that might be needed")
  })).describe("Ordered list of steps to complete the task"),
  risks: z.array(z.string()).optional().describe("Potential risks or challenges"),
  requirements: z.array(z.string()).optional().describe("Requirements or constraints")
});

export class PlannerAgent {
  constructor(private llmManager: LLMManager) {}

  async createPlan(state: GraphState, task: Task): Promise<AgentResponse> {
    try {
      log(AgentType.PLANNER, "Creating plan for task", { taskId: task.id, title: task.title });

      // Gather context about the working environment
      const context = await this.gatherContext(state);
      
      // Generate the plan
      const plan = await this.generatePlan(task, context, state);
      
      // Convert plan to plan items
      const planItems = this.convertPlanToPlanItems(plan);
      
      // Update task with plan
      const updatedTask: Task = {
        ...task,
        planItems,
        status: "in_progress",
        updatedAt: Date.now()
      };

      return {
        agentType: AgentType.PLANNER,
        action: "plan_created",
        result: {
          plan,
          task: updatedTask,
          message: `I've created a plan with ${planItems.length} steps. Here's what I'll do:\n\n${this.formatPlanSummary(plan)}`
        },
        nextAgent: AgentType.PROGRAMMER,
        shouldContinue: true
      };
    } catch (error) {
      log(AgentType.PLANNER, "Error creating plan", { error: error.message });
      throw createError(AgentType.PLANNER, "Failed to create plan", error as Error);
    }
  }

  async refinePlan(state: GraphState, task: Task, feedback: string): Promise<AgentResponse> {
    try {
      log(AgentType.PLANNER, "Refining plan based on feedback", { taskId: task.id, feedback });

      // Analyze current plan and feedback
      const refinedPlan = await this.generateRefinedPlan(task, feedback, state);
      
      // Convert to plan items
      const planItems = this.convertPlanToPlanItems(refinedPlan);
      
      // Update task
      const updatedTask: Task = {
        ...task,
        planItems,
        updatedAt: Date.now()
      };

      return {
        agentType: AgentType.PLANNER,
        action: "plan_refined",
        result: {
          plan: refinedPlan,
          task: updatedTask,
          message: `I've refined the plan based on your feedback. Updated plan:\n\n${this.formatPlanSummary(refinedPlan)}`
        },
        nextAgent: AgentType.PROGRAMMER,
        shouldContinue: true
      };
    } catch (error) {
      log(AgentType.PLANNER, "Error refining plan", { error: error.message });
      throw createError(AgentType.PLANNER, "Failed to refine plan", error as Error);
    }
  }

  private async gatherContext(state: GraphState): Promise<Record<string, any>> {
    const context: Record<string, any> = {
      workingDirectory: state.workingDirectory,
      availableTools: allTools.map(tool => tool.name),
      recentHistory: state.agentHistory.slice(-5)
    };

    try {
      // Try to get directory listing to understand the project structure
      const model = this.llmManager.getModel("planner");
      const toolModel = model.bindTools(allTools);
      
      const response = await toolModel.invoke([
        {
          role: "system",
          content: "You are gathering context about the working environment. Use the list_directory tool to understand the project structure."
        },
        {
          role: "user",
          content: `Please explore the working directory: ${state.workingDirectory}`
        }
      ]);

      if (response.tool_calls && response.tool_calls.length > 0) {
        // Execute the tool call to get directory listing
        const toolCall = response.tool_calls[0];
        if (toolCall.name === "list_directory") {
          const listTool = allTools.find(tool => tool.name === "list_directory");
          if (listTool) {
            const result = await listTool.invoke(toolCall.args);
            context.projectStructure = result;
          }
        }
      }
    } catch (error) {
      log(AgentType.PLANNER, "Could not gather full context", { error: error.message });
      // Continue without full context
    }

    return context;
  }

  private async generatePlan(task: Task, context: Record<string, any>, state: GraphState): Promise<z.infer<typeof PlanSchema>> {
    const model = this.llmManager.getModel("planner");
    
    const systemPrompt = `You are an expert planning agent. Your job is to create detailed, actionable plans for coding tasks.

Available tools: ${allTools.map(tool => `${tool.name}: ${tool.description}`).join(", ")}

Context:
- Working directory: ${context.workingDirectory}
- Project structure: ${JSON.stringify(context.projectStructure, null, 2)}
- Available tools: ${context.availableTools.join(", ")}

Create a comprehensive plan that breaks down the task into manageable steps. Each step should be specific and actionable.
Consider dependencies between steps and estimate time requirements.
Think about potential risks and requirements.`;

    const response = await model.invoke([
      { role: "system", content: systemPrompt },
      { role: "user", content: `Create a plan for this task: ${task.description}` }
    ]);

    // Parse the response to create a structured plan
    // For simplicity, we'll create a basic plan structure
    const content = response.content.toString();
    
    // This is a simplified plan generation - in a real implementation,
    // you'd want to use structured output or more sophisticated parsing
    const steps = this.extractStepsFromResponse(content);
    
    return {
      title: task.title,
      summary: `Plan to complete: ${task.description}`,
      steps,
      risks: ["Unexpected errors during execution", "Missing dependencies"],
      requirements: ["Access to working directory", "Required tools available"]
    };
  }

  private async generateRefinedPlan(task: Task, feedback: string, state: GraphState): Promise<z.infer<typeof PlanSchema>> {
    const model = this.llmManager.getModel("planner");
    
    const currentPlan = {
      steps: task.planItems.map(item => ({
        description: item.description,
        status: item.status
      }))
    };

    const systemPrompt = `You are refining an existing plan based on user feedback.

Current plan: ${JSON.stringify(currentPlan, null, 2)}
User feedback: ${feedback}

Create an improved plan that addresses the feedback while maintaining the overall goal.`;

    const response = await model.invoke([
      { role: "system", content: systemPrompt },
      { role: "user", content: `Please refine the plan for: ${task.description}` }
    ]);

    const content = response.content.toString();
    const steps = this.extractStepsFromResponse(content);
    
    return {
      title: task.title,
      summary: `Refined plan to complete: ${task.description}`,
      steps,
      risks: ["Unexpected errors during execution", "Missing dependencies"],
      requirements: ["Access to working directory", "Required tools available"]
    };
  }

  private extractStepsFromResponse(content: string): Array<{
    description: string;
    type: "analysis" | "implementation" | "testing" | "documentation";
    estimatedTime?: number;
    dependencies?: string[];
    tools?: string[];
  }> {
    // Simple extraction logic - in a real implementation, use more sophisticated parsing
    const lines = content.split('\n').filter(line => line.trim());
    const steps: any[] = [];
    
    let stepCounter = 0;
    for (const line of lines) {
      if (line.match(/^\d+\./) || line.includes('step') || line.includes('Step')) {
        stepCounter++;
        const description = line.replace(/^\d+\.\s*/, '').trim();
        if (description) {
          steps.push({
            description,
            type: this.inferStepType(description),
            estimatedTime: 10, // Default estimate
            dependencies: stepCounter > 1 ? [`step-${stepCounter - 1}`] : [],
            tools: this.inferRequiredTools(description)
          });
        }
      }
    }
    
    // Ensure we have at least one step
    if (steps.length === 0) {
      steps.push({
        description: "Complete the requested task",
        type: "implementation",
        estimatedTime: 15,
        tools: ["shell", "write_file"]
      });
    }
    
    return steps;
  }

  private inferStepType(description: string): "analysis" | "implementation" | "testing" | "documentation" {
    const lower = description.toLowerCase();
    if (lower.includes('analyze') || lower.includes('understand') || lower.includes('review')) {
      return "analysis";
    }
    if (lower.includes('test') || lower.includes('verify')) {
      return "testing";
    }
    if (lower.includes('document') || lower.includes('readme')) {
      return "documentation";
    }
    return "implementation";
  }

  private inferRequiredTools(description: string): string[] {
    const tools: string[] = [];
    const lower = description.toLowerCase();
    
    if (lower.includes('file') || lower.includes('create') || lower.includes('write')) {
      tools.push("write_file", "read_file");
    }
    if (lower.includes('run') || lower.includes('execute') || lower.includes('command')) {
      tools.push("shell");
    }
    if (lower.includes('directory') || lower.includes('folder')) {
      tools.push("list_directory");
    }
    
    return tools.length > 0 ? tools : ["shell"];
  }

  private convertPlanToPlanItems(plan: z.infer<typeof PlanSchema>): PlanItem[] {
    return plan.steps.map((step, index) => createPlanItem(
      step.description,
      step.dependencies
    ));
  }

  private formatPlanSummary(plan: z.infer<typeof PlanSchema>): string {
    return plan.steps.map((step, index) => 
      `${index + 1}. ${step.description} (${step.type}, ~${step.estimatedTime || 10}min)`
    ).join('\n');
  }
}
