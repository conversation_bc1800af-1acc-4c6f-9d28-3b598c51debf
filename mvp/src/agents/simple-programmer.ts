import { AgentType, GraphState, AgentResponse, Task, PlanItem, ToolResult } from "../types/simple.js";
import { 
  log, 
  createError, 
  updatePlanItemStatus, 
  getNextPlanItem, 
  isTaskComplete, 
  hasTaskFailed,
  generateMockResponse,
  delay 
} from "../utils/simple.js";
import { toolRegistry } from "../tools/simple.js";

export class SimpleProgrammerAgent {
  private maxRetries = 3;

  constructor(private mockMode = true) {}

  async executeTask(state: GraphState, task: Task): Promise<AgentResponse> {
    try {
      log(AgentType.PROGRAMMER, "Starting task execution", { taskId: task.id, title: task.title });

      // Get the next plan item to execute
      const nextPlanItem = getNextPlanItem(task);
      
      if (!nextPlanItem) {
        if (isTaskComplete(task)) {
          return this.handleTaskCompletion(task);
        } else if (hasTaskFailed(task)) {
          return this.handleTaskFailure(task);
        } else {
          throw new Error("No available plan items to execute");
        }
      }

      // Execute the plan item
      const result = await this.executePlanItem(state, task, nextPlanItem);
      
      return result;
    } catch (error) {
      log(AgentType.PROGRAMMER, "Error executing task", { error: error.message });
      throw createError(AgentType.PROGRAMMER, "Failed to execute task", error as Error);
    }
  }

  async handleError(state: GraphState, task: Task, error: string): Promise<AgentResponse> {
    try {
      log(AgentType.PROGRAMMER, "Handling error", { taskId: task.id, error });

      // Simulate error analysis
      await delay(500);

      // Analyze the error and determine recovery strategy
      const recovery = this.analyzeError(error, state, task);
      
      if (recovery.canRecover) {
        // Try to recover by executing the recovery action
        const recoveryResult = await this.executeRecoveryAction(state, recovery.action!);
        
        if (recoveryResult.success) {
          return {
            agentType: AgentType.PROGRAMMER,
            action: "error_recovered",
            result: {
              message: `✅ Recovered from error: ${recovery.explanation}\n\nRecovery action: ${recovery.action}`,
              recoveryAction: recovery.action
            },
            nextAgent: AgentType.PROGRAMMER,
            shouldContinue: true
          };
        }
      }

      // If recovery failed or not possible, escalate to planner
      return {
        agentType: AgentType.PROGRAMMER,
        action: "error_escalated",
        result: {
          error,
          message: `❌ Unable to recover from error. Requesting plan revision.\n\nError analysis: ${recovery.explanation}`,
          analysis: recovery.explanation
        },
        nextAgent: AgentType.PLANNER,
        shouldContinue: true
      };
    } catch (recoveryError) {
      log(AgentType.PROGRAMMER, "Error in error handling", { error: recoveryError.message });
      throw createError(AgentType.PROGRAMMER, "Failed to handle error", recoveryError as Error);
    }
  }

  private async executePlanItem(state: GraphState, task: Task, planItem: PlanItem): Promise<AgentResponse> {
    log(AgentType.PROGRAMMER, "Executing plan item", { planItemId: planItem.id, description: planItem.description });

    // Mark plan item as in progress
    const updatedTask = updatePlanItemStatus(task, planItem.id, "in_progress");

    // Simulate execution time
    await delay(1500);

    try {
      // Generate action plan for this specific plan item
      const actions = this.generateActionPlan(state, planItem);
      
      // Execute the actions
      const executionResult = await this.executeActions(state, actions);
      
      if (executionResult.success) {
        // Mark plan item as completed
        const completedTask = updatePlanItemStatus(updatedTask, planItem.id, "completed");
        
        // Check if task is complete
        if (isTaskComplete(completedTask)) {
          return this.handleTaskCompletion(completedTask);
        }
        
        return {
          agentType: AgentType.PROGRAMMER,
          action: "plan_item_completed",
          result: {
            task: completedTask,
            planItem,
            executionResult,
            message: `✅ **Completed**: ${planItem.description}\n\n${executionResult.result}`
          },
          nextAgent: AgentType.PROGRAMMER,
          shouldContinue: true
        };
      } else {
        // Mark plan item as failed
        const failedTask = updatePlanItemStatus(updatedTask, planItem.id, "failed", executionResult.error);
        
        return {
          agentType: AgentType.PROGRAMMER,
          action: "plan_item_failed",
          result: {
            task: failedTask,
            planItem,
            error: executionResult.error,
            message: `❌ **Failed**: ${planItem.description}\n\nError: ${executionResult.error}`
          },
          nextAgent: AgentType.PROGRAMMER,
          shouldContinue: true,
          error: executionResult.error
        };
      }
    } catch (error) {
      // Mark plan item as failed
      const failedTask = updatePlanItemStatus(updatedTask, planItem.id, "failed", error.message);
      
      return {
        agentType: AgentType.PROGRAMMER,
        action: "plan_item_failed",
        result: {
          task: failedTask,
          planItem,
          error: error.message,
          message: `❌ **Failed**: ${planItem.description}\n\nError: ${error.message}`
        },
        nextAgent: AgentType.PROGRAMMER,
        shouldContinue: true,
        error: error.message
      };
    }
  }

  private generateActionPlan(state: GraphState, planItem: PlanItem): Array<{
    tool: string;
    args: Record<string, any>;
    description: string;
  }> {
    const description = planItem.description.toLowerCase();
    const actions: Array<{ tool: string; args: Record<string, any>; description: string }> = [];

    // Generate actions based on plan item description
    if (description.includes("analyze") || description.includes("gather")) {
      actions.push({
        tool: "list_directory",
        args: { directoryPath: state.workingDirectory },
        description: "Analyzing project structure"
      });
    }

    if (description.includes("function") || description.includes("calculator")) {
      actions.push({
        tool: "write_file",
        args: {
          filePath: `${state.workingDirectory}/calculator.js`,
          content: this.generateCalculatorCode()
        },
        description: "Creating calculator function"
      });
    }

    if (description.includes("readme") || description.includes("documentation")) {
      actions.push({
        tool: "write_file",
        args: {
          filePath: `${state.workingDirectory}/README.md`,
          content: this.generateReadmeContent()
        },
        description: "Creating README documentation"
      });
    }

    if (description.includes("test")) {
      actions.push({
        tool: "write_file",
        args: {
          filePath: `${state.workingDirectory}/test.js`,
          content: this.generateTestCode()
        },
        description: "Creating test file"
      });
      actions.push({
        tool: "shell",
        args: { 
          command: "node test.js",
          workingDirectory: state.workingDirectory
        },
        description: "Running tests"
      });
    }

    if (description.includes("server")) {
      actions.push({
        tool: "write_file",
        args: {
          filePath: `${state.workingDirectory}/server.js`,
          content: this.generateServerCode()
        },
        description: "Creating web server"
      });
    }

    // Default action if no specific actions generated
    if (actions.length === 0) {
      actions.push({
        tool: "shell",
        args: { 
          command: `echo "Executing: ${planItem.description}"`,
          workingDirectory: state.workingDirectory
        },
        description: `Default action for: ${planItem.description}`
      });
    }

    return actions;
  }

  private async executeActions(state: GraphState, actions: Array<{
    tool: string;
    args: Record<string, any>;
    description: string;
  }>): Promise<ToolResult> {
    const results: string[] = [];
    
    for (const action of actions) {
      log(AgentType.PROGRAMMER, "Executing action", { tool: action.tool, description: action.description });
      
      try {
        const tool = toolRegistry[action.tool as keyof typeof toolRegistry];
        if (!tool) {
          throw new Error(`Unknown tool: ${action.tool}`);
        }

        let result: ToolResult;
        
        // Call the appropriate tool function
        switch (action.tool) {
          case "shell":
            result = await tool(action.args.command, action.args.workingDirectory, action.args.timeout);
            break;
          case "write_file":
            result = await tool(action.args.filePath, action.args.content, action.args.createDirectories);
            break;
          case "read_file":
            result = await tool(action.args.filePath);
            break;
          case "list_directory":
            result = await tool(action.args.directoryPath, action.args.includeHidden);
            break;
          default:
            throw new Error(`Unsupported tool: ${action.tool}`);
        }
        
        if (result.success) {
          results.push(`✓ ${action.description}: ${result.result}`);
        } else {
          return {
            success: false,
            result: results.join('\n'),
            error: `Action failed: ${result.error}`,
            metadata: { failedAction: action }
          };
        }
      } catch (error) {
        return {
          success: false,
          result: results.join('\n'),
          error: `Action failed: ${error.message}`,
          metadata: { failedAction: action }
        };
      }
    }

    return {
      success: true,
      result: results.join('\n'),
      metadata: { actionsExecuted: actions.length }
    };
  }

  private analyzeError(error: string, state: GraphState, task: Task): {
    canRecover: boolean;
    action?: string;
    explanation: string;
  } {
    const lowerError = error.toLowerCase();
    
    // Simple error analysis
    if (lowerError.includes("no such file") || lowerError.includes("not found")) {
      return {
        canRecover: true,
        action: "create_missing_directory",
        explanation: "Missing file or directory detected. Can create the required structure."
      };
    }
    
    if (lowerError.includes("permission") || lowerError.includes("access")) {
      return {
        canRecover: true,
        action: "fix_permissions",
        explanation: "Permission issue detected. Can attempt to fix permissions."
      };
    }
    
    if (lowerError.includes("syntax") || lowerError.includes("parse")) {
      return {
        canRecover: false,
        explanation: "Syntax error detected. Requires manual code review and correction."
      };
    }

    return {
      canRecover: false,
      explanation: "Unknown error type. Manual intervention required."
    };
  }

  private async executeRecoveryAction(state: GraphState, action: string): Promise<ToolResult> {
    switch (action) {
      case "create_missing_directory":
        return await toolRegistry.shell(`mkdir -p ${state.workingDirectory}`, state.workingDirectory);
      
      case "fix_permissions":
        return await toolRegistry.shell(`chmod 755 ${state.workingDirectory}`, state.workingDirectory);
      
      default:
        return {
          success: false,
          result: "",
          error: `Unknown recovery action: ${action}`
        };
    }
  }

  private handleTaskCompletion(task: Task): AgentResponse {
    const completedItems = task.planItems.filter(item => item.status === "completed");
    
    return {
      agentType: AgentType.PROGRAMMER,
      action: "task_completed",
      result: {
        task,
        message: `🎉 **Task completed successfully!**

**${task.title}**

✅ Completed ${completedItems.length}/${task.planItems.length} plan items
⏱️ Total execution time: ${Math.round((Date.now() - task.createdAt) / 1000)}s

All requested functionality has been implemented and tested.`,
        summary: `Completed ${task.planItems.length} plan items`
      },
      nextAgent: AgentType.MANAGER,
      shouldContinue: false
    };
  }

  private handleTaskFailure(task: Task): AgentResponse {
    const failedItems = task.planItems.filter(item => item.status === "failed");
    
    return {
      agentType: AgentType.PROGRAMMER,
      action: "task_failed",
      result: {
        task,
        message: `❌ **Task failed**: ${task.title}

Failed items: ${failedItems.length}
- ${failedItems.map(item => item.description).join('\n- ')}

Requesting plan revision to address the failures.`,
        failedItems,
        summary: `${failedItems.length} plan items failed`
      },
      nextAgent: AgentType.PLANNER,
      shouldContinue: true,
      error: `Task failed with ${failedItems.length} failed plan items`
    };
  }

  // Code generation helpers
  private generateCalculatorCode(): string {
    return `// Simple Calculator Function
function calculator(operation, a, b) {
  switch (operation) {
    case 'add':
      return a + b;
    case 'subtract':
      return a - b;
    case 'multiply':
      return a * b;
    case 'divide':
      return b !== 0 ? a / b : 'Error: Division by zero';
    default:
      return 'Error: Invalid operation';
  }
}

// Example usage
console.log('Calculator Examples:');
console.log('5 + 3 =', calculator('add', 5, 3));
console.log('10 - 4 =', calculator('subtract', 10, 4));
console.log('6 * 7 =', calculator('multiply', 6, 7));
console.log('15 / 3 =', calculator('divide', 15, 3));

module.exports = calculator;
`;
  }

  private generateTestCode(): string {
    return `// Test file
const calculator = require('./calculator.js');

console.log('Running calculator tests...');

// Test cases
const tests = [
  { op: 'add', a: 2, b: 3, expected: 5 },
  { op: 'subtract', a: 10, b: 4, expected: 6 },
  { op: 'multiply', a: 3, b: 4, expected: 12 },
  { op: 'divide', a: 15, b: 3, expected: 5 }
];

let passed = 0;
tests.forEach((test, index) => {
  const result = calculator(test.op, test.a, test.b);
  if (result === test.expected) {
    console.log(\`✓ Test \${index + 1} passed\`);
    passed++;
  } else {
    console.log(\`✗ Test \${index + 1} failed: expected \${test.expected}, got \${result}\`);
  }
});

console.log(\`\nTests completed: \${passed}/\${tests.length} passed\`);
`;
  }

  private generateServerCode(): string {
    return `// Simple Web Server
const http = require('http');
const url = require('url');

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  
  res.writeHead(200, { 'Content-Type': 'application/json' });
  
  if (parsedUrl.pathname === '/') {
    res.end(JSON.stringify({ message: 'Hello World!', timestamp: new Date().toISOString() }));
  } else if (parsedUrl.pathname === '/health') {
    res.end(JSON.stringify({ status: 'OK', uptime: process.uptime() }));
  } else {
    res.writeHead(404);
    res.end(JSON.stringify({ error: 'Not Found' }));
  }
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
});
`;
  }

  private generateReadmeContent(): string {
    return `# Project Documentation

## Overview
This project was created by the Open SWE MVP multi-agent system.

## Features
- Automated code generation
- Multi-agent coordination
- Task planning and execution

## Usage
Follow the instructions provided by the system for specific functionality.

## Generated Files
- \`calculator.js\` - Calculator function implementation
- \`test.js\` - Test cases for validation
- \`server.js\` - Basic web server (if applicable)

## System Information
- Generated by: Open SWE MVP
- Timestamp: ${new Date().toISOString()}
- Agent: Programmer Agent

---
*This documentation was automatically generated.*
`;
  }
}
