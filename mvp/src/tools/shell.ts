import { spawn } from "child_process";
import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { ToolResult } from "../types/index.js";

const ShellToolSchema = z.object({
  command: z.string().describe("The shell command to execute"),
  workingDirectory: z.string().optional().describe("Working directory for the command"),
  timeout: z.number().optional().default(30).describe("Timeout in seconds")
});

export const shellTool = tool(
  async (input): Promise<ToolResult> => {
    const { command, workingDirectory, timeout } = input;
    
    return new Promise((resolve) => {
      const child = spawn("sh", ["-c", command], {
        cwd: workingDirectory || process.cwd(),
        stdio: ["pipe", "pipe", "pipe"],
        timeout: timeout * 1000
      });

      let stdout = "";
      let stderr = "";

      child.stdout?.on("data", (data) => {
        stdout += data.toString();
      });

      child.stderr?.on("data", (data) => {
        stderr += data.toString();
      });

      child.on("close", (code) => {
        if (code === 0) {
          resolve({
            success: true,
            result: stdout.trim(),
            metadata: { exitCode: code }
          });
        } else {
          resolve({
            success: false,
            result: stderr.trim() || stdout.trim(),
            error: `Command failed with exit code ${code}`,
            metadata: { exitCode: code }
          });
        }
      });

      child.on("error", (error) => {
        resolve({
          success: false,
          result: "",
          error: error.message,
          metadata: { error: error.name }
        });
      });

      // Handle timeout
      setTimeout(() => {
        if (!child.killed) {
          child.kill();
          resolve({
            success: false,
            result: "",
            error: `Command timed out after ${timeout} seconds`,
            metadata: { timeout: true }
          });
        }
      }, timeout * 1000);
    });
  },
  {
    name: "shell",
    description: "Execute shell commands in the local environment",
    schema: ShellToolSchema
  }
);
