import { readFile, writeFile, readdir, stat, mkdir } from "fs/promises";
import { join, dirname } from "path";
import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { ToolResult } from "../types/index.js";

// Read file tool
const ReadFileSchema = z.object({
  filePath: z.string().describe("Path to the file to read"),
  encoding: z.string().optional().default("utf8").describe("File encoding")
});

export const readFileTool = tool(
  async (input): Promise<ToolResult> => {
    try {
      const { filePath, encoding } = input;
      const content = await readFile(filePath, encoding as BufferEncoding);
      return {
        success: true,
        result: content,
        metadata: { filePath, size: content.length }
      };
    } catch (error) {
      return {
        success: false,
        result: "",
        error: error instanceof Error ? error.message : String(error),
        metadata: { filePath: input.filePath }
      };
    }
  },
  {
    name: "read_file",
    description: "Read the contents of a file",
    schema: ReadFileSchema
  }
);

// Write file tool
const WriteFileSchema = z.object({
  filePath: z.string().describe("Path to the file to write"),
  content: z.string().describe("Content to write to the file"),
  createDirectories: z.boolean().optional().default(true).describe("Create parent directories if they don't exist")
});

export const writeFileTool = tool(
  async (input): Promise<ToolResult> => {
    try {
      const { filePath, content, createDirectories } = input;
      
      if (createDirectories) {
        const dir = dirname(filePath);
        await mkdir(dir, { recursive: true });
      }
      
      await writeFile(filePath, content, "utf8");
      return {
        success: true,
        result: `Successfully wrote ${content.length} characters to ${filePath}`,
        metadata: { filePath, size: content.length }
      };
    } catch (error) {
      return {
        success: false,
        result: "",
        error: error instanceof Error ? error.message : String(error),
        metadata: { filePath: input.filePath }
      };
    }
  },
  {
    name: "write_file",
    description: "Write content to a file",
    schema: WriteFileSchema
  }
);

// List directory tool
const ListDirectorySchema = z.object({
  directoryPath: z.string().describe("Path to the directory to list"),
  includeHidden: z.boolean().optional().default(false).describe("Include hidden files and directories")
});

export const listDirectoryTool = tool(
  async (input): Promise<ToolResult> => {
    try {
      const { directoryPath, includeHidden } = input;
      const items = await readdir(directoryPath);
      
      const filteredItems = includeHidden 
        ? items 
        : items.filter(item => !item.startsWith('.'));
      
      const itemsWithStats = await Promise.all(
        filteredItems.map(async (item) => {
          try {
            const itemPath = join(directoryPath, item);
            const stats = await stat(itemPath);
            return {
              name: item,
              type: stats.isDirectory() ? "directory" : "file",
              size: stats.size,
              modified: stats.mtime.toISOString()
            };
          } catch {
            return {
              name: item,
              type: "unknown",
              size: 0,
              modified: ""
            };
          }
        })
      );
      
      return {
        success: true,
        result: JSON.stringify(itemsWithStats, null, 2),
        metadata: { directoryPath, itemCount: itemsWithStats.length }
      };
    } catch (error) {
      return {
        success: false,
        result: "",
        error: error instanceof Error ? error.message : String(error),
        metadata: { directoryPath: input.directoryPath }
      };
    }
  },
  {
    name: "list_directory",
    description: "List the contents of a directory",
    schema: ListDirectorySchema
  }
);
