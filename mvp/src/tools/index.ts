export { shellTool } from "./shell.js";
export { readFileTool, writeFileTool, listDirectoryTool } from "./file-operations.js";

import { shellTool } from "./shell.js";
import { readFileTool, writeFileTool, listDirectoryTool } from "./file-operations.js";

// Export all tools as an array for easy binding
export const allTools = [
  shellTool,
  readFileTool,
  writeFileTool,
  listDirectoryTool
];

// Tool registry for dynamic access
export const toolRegistry = {
  shell: shellTool,
  read_file: readFileTool,
  write_file: writeFileTool,
  list_directory: listDirectoryTool
};

export type ToolName = keyof typeof toolRegistry;
