import { spawn } from "child_process";
import { readFile, writeFile, readdir, stat, mkdir } from "fs/promises";
import { join, dirname } from "path";
import { ToolResult } from "../types/simple.js";

// Simple shell execution tool
export async function executeShell(command: string, workingDirectory?: string, timeout = 30): Promise<ToolResult> {
  return new Promise((resolve) => {
    const child = spawn("sh", ["-c", command], {
      cwd: workingDirectory || process.cwd(),
      stdio: ["pipe", "pipe", "pipe"],
      timeout: timeout * 1000
    });

    let stdout = "";
    let stderr = "";

    child.stdout?.on("data", (data) => {
      stdout += data.toString();
    });

    child.stderr?.on("data", (data) => {
      stderr += data.toString();
    });

    child.on("close", (code) => {
      if (code === 0) {
        resolve({
          success: true,
          result: stdout.trim(),
          metadata: { exitCode: code }
        });
      } else {
        resolve({
          success: false,
          result: stderr.trim() || stdout.trim(),
          error: `Command failed with exit code ${code}`,
          metadata: { exitCode: code }
        });
      }
    });

    child.on("error", (error) => {
      resolve({
        success: false,
        result: "",
        error: error.message,
        metadata: { error: error.name }
      });
    });

    // Handle timeout
    setTimeout(() => {
      if (!child.killed) {
        child.kill();
        resolve({
          success: false,
          result: "",
          error: `Command timed out after ${timeout} seconds`,
          metadata: { timeout: true }
        });
      }
    }, timeout * 1000);
  });
}

// Simple file read tool
export async function readFileContent(filePath: string): Promise<ToolResult> {
  try {
    const content = await readFile(filePath, "utf8");
    return {
      success: true,
      result: content,
      metadata: { filePath, size: content.length }
    };
  } catch (error) {
    return {
      success: false,
      result: "",
      error: error instanceof Error ? error.message : String(error),
      metadata: { filePath }
    };
  }
}

// Simple file write tool
export async function writeFileContent(filePath: string, content: string, createDirectories = true): Promise<ToolResult> {
  try {
    if (createDirectories) {
      const dir = dirname(filePath);
      await mkdir(dir, { recursive: true });
    }
    
    await writeFile(filePath, content, "utf8");
    return {
      success: true,
      result: `Successfully wrote ${content.length} characters to ${filePath}`,
      metadata: { filePath, size: content.length }
    };
  } catch (error) {
    return {
      success: false,
      result: "",
      error: error instanceof Error ? error.message : String(error),
      metadata: { filePath }
    };
  }
}

// Simple directory listing tool
export async function listDirectory(directoryPath: string, includeHidden = false): Promise<ToolResult> {
  try {
    const items = await readdir(directoryPath);
    
    const filteredItems = includeHidden 
      ? items 
      : items.filter(item => !item.startsWith('.'));
    
    const itemsWithStats = await Promise.all(
      filteredItems.map(async (item) => {
        try {
          const itemPath = join(directoryPath, item);
          const stats = await stat(itemPath);
          return {
            name: item,
            type: stats.isDirectory() ? "directory" : "file",
            size: stats.size,
            modified: stats.mtime.toISOString()
          };
        } catch {
          return {
            name: item,
            type: "unknown",
            size: 0,
            modified: ""
          };
        }
      })
    );
    
    return {
      success: true,
      result: JSON.stringify(itemsWithStats, null, 2),
      metadata: { directoryPath, itemCount: itemsWithStats.length }
    };
  } catch (error) {
    return {
      success: false,
      result: "",
      error: error instanceof Error ? error.message : String(error),
      metadata: { directoryPath }
    };
  }
}

// Tool registry for easy access
export const toolRegistry = {
  shell: executeShell,
  read_file: readFileContent,
  write_file: writeFileContent,
  list_directory: listDirectory
};

export type ToolName = keyof typeof toolRegistry;
