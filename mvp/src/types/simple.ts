// Simplified types for MVP demonstration without external dependencies

export interface BaseMessage {
  id: string;
  content: string;
  role: "user" | "assistant" | "system";
  timestamp: number;
}

export enum AgentType {
  MANAGER = "manager",
  PLANNER = "planner", 
  PROGRAMMER = "programmer"
}

export interface PlanItem {
  id: string;
  description: string;
  status: "pending" | "in_progress" | "completed" | "failed";
  dependencies?: string[];
  estimatedTime?: number;
  actualTime?: number;
  error?: string;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  status: "pending" | "in_progress" | "completed" | "failed";
  createdAt: number;
  updatedAt: number;
  planItems: PlanItem[];
  currentPlanItemIndex: number;
}

export interface GraphState {
  messages: BaseMessage[];
  currentTask?: Task;
  workingDirectory: string;
  context: Record<string, any>;
  lastError?: string;
  agentHistory: {
    agentType: AgentType;
    action: string;
    timestamp: number;
    result?: any;
  }[];
}

export interface AgentConfig {
  workingDirectory: string;
  debug: boolean;
  mockMode: boolean; // For demonstration without real LLM
}

export interface ToolResult {
  success: boolean;
  result: string;
  error?: string;
  metadata?: Record<string, any>;
}

export interface AgentResponse {
  agentType: AgentType;
  action: string;
  result: any;
  nextAgent?: AgentType;
  shouldContinue: boolean;
  error?: string;
}

export interface ProcessRequest {
  userInput: string;
  workingDirectory?: string;
  context?: Record<string, any>;
}

export interface ProcessResult {
  success: boolean;
  task: Task;
  finalResult: string;
  error?: string;
  executionTime: number;
}
