import { z } from "zod";
import { BaseMessage } from "@langchain/core/messages";

// Basic message types
export interface UserMessage {
  id: string;
  content: string;
  timestamp: number;
}

export interface AgentMessage {
  id: string;
  content: string;
  agentType: AgentType;
  timestamp: number;
}

// Agent types
export enum AgentType {
  MANAGER = "manager",
  PLANNER = "planner", 
  PROGRAMMER = "programmer"
}

// Task and plan structures
export interface PlanItem {
  id: string;
  description: string;
  status: "pending" | "in_progress" | "completed" | "failed";
  dependencies?: string[];
  estimatedTime?: number;
  actualTime?: number;
  error?: string;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  status: "pending" | "in_progress" | "completed" | "failed";
  createdAt: number;
  updatedAt: number;
  planItems: PlanItem[];
  currentPlanItemIndex: number;
}

// Graph state for LangGraph
export interface GraphState {
  messages: BaseMessage[];
  currentTask?: Task;
  workingDirectory: string;
  context: Record<string, any>;
  lastError?: string;
  agentHistory: {
    agentType: AgentType;
    action: string;
    timestamp: number;
    result?: any;
  }[];
}

// Configuration
export interface AgentConfig {
  modelProvider: "anthropic" | "openai";
  modelName: string;
  temperature: number;
  maxTokens?: number;
  workingDirectory: string;
  debug: boolean;
}

// Tool execution results
export interface ToolResult {
  success: boolean;
  result: string;
  error?: string;
  metadata?: Record<string, any>;
}

// Agent responses
export interface AgentResponse {
  agentType: AgentType;
  action: string;
  result: any;
  nextAgent?: AgentType;
  shouldContinue: boolean;
  error?: string;
}

// Request types
export interface ProcessRequest {
  userInput: string;
  workingDirectory?: string;
  context?: Record<string, any>;
}

export interface ProcessResult {
  success: boolean;
  task: Task;
  finalResult: string;
  error?: string;
  executionTime: number;
}

// Zod schemas for validation
export const PlanItemSchema = z.object({
  id: z.string(),
  description: z.string(),
  status: z.enum(["pending", "in_progress", "completed", "failed"]),
  dependencies: z.array(z.string()).optional(),
  estimatedTime: z.number().optional(),
  actualTime: z.number().optional(),
  error: z.string().optional()
});

export const TaskSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  status: z.enum(["pending", "in_progress", "completed", "failed"]),
  createdAt: z.number(),
  updatedAt: z.number(),
  planItems: z.array(PlanItemSchema),
  currentPlanItemIndex: z.number()
});

export const GraphStateSchema = z.object({
  messages: z.array(z.any()),
  currentTask: TaskSchema.optional(),
  workingDirectory: z.string(),
  context: z.record(z.any()),
  lastError: z.string().optional(),
  agentHistory: z.array(z.object({
    agentType: z.nativeEnum(AgentType),
    action: z.string(),
    timestamp: z.number(),
    result: z.any().optional()
  }))
});

export type GraphStateType = z.infer<typeof GraphStateSchema>;
