// MVP Types - Simple StateGraph implementation
export interface GraphState {
  messages: string[];
  currentAgent: string;
  userInput: string;
  result: string;
  isComplete: boolean;
  error?: string;
}

export interface AgentNode {
  name: string;
  execute: (state: GraphState) => Promise<GraphState>;
}

export interface Edge {
  from: string;
  to: string;
  condition?: (state: GraphState) => boolean;
}

export class SimpleStateGraph {
  private nodes: Map<string, AgentNode> = new Map();
  private edges: Edge[] = [];
  private startNode: string = "";

  addNode(node: AgentNode): void {
    this.nodes.set(node.name, node);
  }

  addEdge(from: string, to: string, condition?: (state: GraphState) => boolean): void {
    this.edges.push({ from, to, condition });
  }

  setStart(nodeName: string): void {
    this.startNode = nodeName;
  }

  async execute(initialState: GraphState): Promise<GraphState> {
    let currentState = { ...initialState };
    let currentNode = this.startNode;
    let iterations = 0;
    const maxIterations = 10;

    while (!currentState.isComplete && iterations < maxIterations) {
      iterations++;
      
      const node = this.nodes.get(currentNode);
      if (!node) {
        throw new Error(`Node ${currentNode} not found`);
      }

      console.log(`[StateGraph] Executing node: ${currentNode}`);
      currentState = await node.execute(currentState);
      currentState.currentAgent = currentNode;

      if (currentState.isComplete) {
        break;
      }

      // Find next node
      const nextEdge = this.edges.find(edge => 
        edge.from === currentNode && 
        (!edge.condition || edge.condition(currentState))
      );

      if (!nextEdge) {
        console.log(`[StateGraph] No next node found for ${currentNode}, ending execution`);
        currentState.isComplete = true;
        break;
      }

      currentNode = nextEdge.to;
    }

    if (iterations >= maxIterations) {
      currentState.error = "Maximum iterations reached";
      currentState.isComplete = true;
    }

    return currentState;
  }
}

// Test tool interface
export interface TestTool {
  name: string;
  execute: (input: string) => Promise<string>;
}

export const echoTool: TestTool = {
  name: "echo",
  execute: async (input: string) => {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate work
    return `Echo: ${input}`;
  }
};
