import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { StateGraph, END, START } from "@langchain/langgraph";
import { v4 as uuidv4 } from "uuid";
import { 
  AgentType, 
  GraphState, 
  AgentResponse, 
  ProcessRequest, 
  ProcessResult,
  Task
} from "./types/index.js";
import { ManagerAgent, PlannerAgent, ProgrammerAgent } from "./agents/index.js";
import { LLMManager, log, createError } from "./utils/index.js";

export class MultiAgentCoordinator {
  private manager: ManagerAgent;
  private planner: PlannerAgent;
  private programmer: ProgrammerAgent;
  private maxIterations = 20;

  constructor(private llmManager: LLMManager) {
    this.manager = new ManagerAgent(llmManager);
    this.planner = new PlannerAgent(llmManager);
    this.programmer = new ProgrammerAgent(llmManager);
  }

  async processRequest(request: ProcessRequest): Promise<ProcessResult> {
    const startTime = Date.now();
    
    try {
      log(AgentType.MANAGER, "Processing new request", { input: request.userInput });

      // Initialize state
      const initialState: GraphState = {
        messages: [new HumanMessage({ content: request.userInput })],
        workingDirectory: request.workingDirectory || process.env.WORK_DIR || "./workspace",
        context: request.context || {},
        agentHistory: []
      };

      // Execute the multi-agent workflow
      const result = await this.executeWorkflow(initialState, request.userInput);
      
      const executionTime = Date.now() - startTime;
      
      return {
        success: true,
        task: result.task,
        finalResult: result.message,
        executionTime
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      log(AgentType.MANAGER, "Request processing failed", { error: error.message });
      
      return {
        success: false,
        task: {
          id: uuidv4(),
          title: "Failed Task",
          description: request.userInput,
          status: "failed",
          createdAt: startTime,
          updatedAt: Date.now(),
          planItems: [],
          currentPlanItemIndex: 0
        },
        finalResult: `Failed to process request: ${error.message}`,
        error: error.message,
        executionTime
      };
    }
  }

  private async executeWorkflow(state: GraphState, userInput: string): Promise<{
    task: Task;
    message: string;
  }> {
    let currentState = { ...state };
    let currentAgent: AgentType = AgentType.MANAGER;
    let iterations = 0;
    let lastResponse: AgentResponse | null = null;

    while (iterations < this.maxIterations) {
      iterations++;
      
      log(currentAgent, `Iteration ${iterations}`, { agent: currentAgent });

      try {
        // Execute current agent
        const response = await this.executeAgent(currentAgent, currentState, userInput, lastResponse);
        
        // Update state with agent response
        currentState = this.updateState(currentState, response);
        
        // Add to agent history
        currentState.agentHistory.push({
          agentType: response.agentType,
          action: response.action,
          timestamp: Date.now(),
          result: response.result
        });

        // Check if workflow should continue
        if (!response.shouldContinue) {
          log(AgentType.MANAGER, "Workflow completed", { 
            iterations, 
            finalAgent: response.agentType,
            action: response.action 
          });
          
          return {
            task: currentState.currentTask!,
            message: response.result.message || "Task completed successfully"
          };
        }

        // Handle errors
        if (response.error) {
          log(response.agentType, "Agent reported error", { error: response.error });
          
          // If programmer reports error, let it handle it
          if (response.agentType === AgentType.PROGRAMMER && response.nextAgent === AgentType.PROGRAMMER) {
            currentAgent = AgentType.PROGRAMMER;
            lastResponse = response;
            continue;
          }
          
          // Otherwise route to next agent as specified
          if (response.nextAgent) {
            currentAgent = response.nextAgent;
            lastResponse = response;
            continue;
          }
        }

        // Route to next agent
        if (response.nextAgent) {
          currentAgent = response.nextAgent;
          lastResponse = response;
        } else {
          // No next agent specified, workflow ends
          break;
        }

      } catch (error) {
        log(currentAgent, "Agent execution failed", { error: error.message, iterations });
        
        // Try to recover by routing to manager
        if (currentAgent !== AgentType.MANAGER) {
          currentAgent = AgentType.MANAGER;
          currentState.lastError = error.message;
          continue;
        } else {
          // Manager failed, can't recover
          throw error;
        }
      }
    }

    // If we reach here, we hit max iterations
    log(AgentType.MANAGER, "Workflow reached max iterations", { iterations });
    
    return {
      task: currentState.currentTask || {
        id: uuidv4(),
        title: "Incomplete Task",
        description: userInput,
        status: "failed",
        createdAt: Date.now(),
        updatedAt: Date.now(),
        planItems: [],
        currentPlanItemIndex: 0
      },
      message: "Workflow reached maximum iterations without completion"
    };
  }

  private async executeAgent(
    agentType: AgentType, 
    state: GraphState, 
    userInput: string,
    lastResponse: AgentResponse | null
  ): Promise<AgentResponse> {
    switch (agentType) {
      case AgentType.MANAGER:
        if (lastResponse?.error) {
          // Manager handling error from other agents
          return {
            agentType: AgentType.MANAGER,
            action: "error_handled",
            result: {
              message: `I encountered an issue: ${lastResponse.error}. Let me try a different approach.`,
              error: lastResponse.error
            },
            nextAgent: AgentType.PLANNER,
            shouldContinue: true
          };
        }
        return await this.manager.processMessage(state, userInput);

      case AgentType.PLANNER:
        if (!state.currentTask) {
          throw new Error("No task available for planning");
        }
        
        if (lastResponse?.error) {
          // Planner refining plan due to error
          return await this.planner.refinePlan(state, state.currentTask, lastResponse.error);
        }
        
        return await this.planner.createPlan(state, state.currentTask);

      case AgentType.PROGRAMMER:
        if (!state.currentTask) {
          throw new Error("No task available for execution");
        }
        
        if (lastResponse?.error) {
          // Programmer handling error
          return await this.programmer.handleError(state, state.currentTask, lastResponse.error);
        }
        
        return await this.programmer.executeTask(state, state.currentTask);

      default:
        throw new Error(`Unknown agent type: ${agentType}`);
    }
  }

  private updateState(state: GraphState, response: AgentResponse): GraphState {
    const newState = { ...state };

    // Update messages
    if (response.result.message) {
      newState.messages = [
        ...state.messages,
        new AIMessage({ 
          content: response.result.message,
          additional_kwargs: { agentType: response.agentType }
        })
      ];
    }

    // Update current task
    if (response.result.task) {
      newState.currentTask = response.result.task;
    }

    // Update context
    if (response.result.context) {
      newState.context = { ...state.context, ...response.result.context };
    }

    // Update last error
    if (response.error) {
      newState.lastError = response.error;
    } else {
      delete newState.lastError;
    }

    return newState;
  }

  // Method to get current state for debugging
  getCurrentState(): Partial<GraphState> {
    return {
      // Return safe subset of state for debugging
      workingDirectory: process.env.WORK_DIR || "./workspace",
      context: {},
      agentHistory: []
    };
  }
}
