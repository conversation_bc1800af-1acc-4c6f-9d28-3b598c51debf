import { v4 as uuidv4 } from "uuid";
import { 
  AgentType, 
  GraphState, 
  AgentResponse, 
  ProcessRequest, 
  ProcessResult,
  Task,
  BaseMessage
} from "./types/simple.js";
import { SimpleManagerAgent } from "./agents/simple-manager.js";
import { SimplePlannerAgent } from "./agents/simple-planner.js";
import { SimpleProgrammerAgent } from "./agents/simple-programmer.js";
import { log, createError, createMessage } from "./utils/simple.js";

export class SimpleMultiAgentCoordinator {
  private manager: SimpleManagerAgent;
  private planner: SimplePlannerAgent;
  private programmer: SimpleProgrammerAgent;
  private maxIterations = 20;

  constructor(private mockMode = true) {
    this.manager = new SimpleManagerAgent(mockMode);
    this.planner = new SimplePlannerAgent(mockMode);
    this.programmer = new SimpleProgrammerAgent(mockMode);
  }

  async processRequest(request: ProcessRequest): Promise<ProcessResult> {
    const startTime = Date.now();
    
    try {
      log(AgentType.MANAGER, "Processing new request", { input: request.userInput });

      // Initialize state
      const initialState: GraphState = {
        messages: [createMessage(request.userInput, "user")],
        workingDirectory: request.workingDirectory || process.env.WORK_DIR || "./workspace",
        context: request.context || {},
        agentHistory: []
      };

      // Execute the multi-agent workflow
      const result = await this.executeWorkflow(initialState, request.userInput);
      
      const executionTime = Date.now() - startTime;
      
      return {
        success: true,
        task: result.task,
        finalResult: result.message,
        executionTime
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      log(AgentType.MANAGER, "Request processing failed", { error: error.message });
      
      return {
        success: false,
        task: {
          id: uuidv4(),
          title: "Failed Task",
          description: request.userInput,
          status: "failed",
          createdAt: startTime,
          updatedAt: Date.now(),
          planItems: [],
          currentPlanItemIndex: 0
        },
        finalResult: `Failed to process request: ${error.message}`,
        error: error.message,
        executionTime
      };
    }
  }

  private async executeWorkflow(state: GraphState, userInput: string): Promise<{
    task: Task;
    message: string;
  }> {
    let currentState = { ...state };
    let currentAgent: AgentType = AgentType.MANAGER;
    let iterations = 0;
    let lastResponse: AgentResponse | null = null;
    let allMessages: string[] = [];

    while (iterations < this.maxIterations) {
      iterations++;
      
      log(currentAgent, `Iteration ${iterations}`, { agent: currentAgent });

      try {
        // Execute current agent
        const response = await this.executeAgent(currentAgent, currentState, userInput, lastResponse);
        
        // Collect response message
        if (response.result.message) {
          allMessages.push(`**${response.agentType.toUpperCase()}**: ${response.result.message}`);
        }
        
        // Update state with agent response
        currentState = this.updateState(currentState, response);
        
        // Add to agent history
        currentState.agentHistory.push({
          agentType: response.agentType,
          action: response.action,
          timestamp: Date.now(),
          result: response.result
        });

        // Check if workflow should continue
        if (!response.shouldContinue) {
          log(AgentType.MANAGER, "Workflow completed", { 
            iterations, 
            finalAgent: response.agentType,
            action: response.action 
          });
          
          return {
            task: currentState.currentTask!,
            message: this.formatFinalMessage(allMessages, response)
          };
        }

        // Handle errors
        if (response.error) {
          log(response.agentType, "Agent reported error", { error: response.error });
          
          // If programmer reports error, let it handle it
          if (response.agentType === AgentType.PROGRAMMER && response.nextAgent === AgentType.PROGRAMMER) {
            currentAgent = AgentType.PROGRAMMER;
            lastResponse = response;
            continue;
          }
          
          // Otherwise route to next agent as specified
          if (response.nextAgent) {
            currentAgent = response.nextAgent;
            lastResponse = response;
            continue;
          }
        }

        // Route to next agent
        if (response.nextAgent) {
          currentAgent = response.nextAgent;
          lastResponse = response;
        } else {
          // No next agent specified, workflow ends
          break;
        }

      } catch (error) {
        log(currentAgent, "Agent execution failed", { error: error.message, iterations });
        
        // Try to recover by routing to manager
        if (currentAgent !== AgentType.MANAGER) {
          currentAgent = AgentType.MANAGER;
          currentState.lastError = error.message;
          allMessages.push(`**ERROR**: ${error.message}`);
          continue;
        } else {
          // Manager failed, can't recover
          throw error;
        }
      }
    }

    // If we reach here, we hit max iterations
    log(AgentType.MANAGER, "Workflow reached max iterations", { iterations });
    
    return {
      task: currentState.currentTask || {
        id: uuidv4(),
        title: "Incomplete Task",
        description: userInput,
        status: "failed",
        createdAt: Date.now(),
        updatedAt: Date.now(),
        planItems: [],
        currentPlanItemIndex: 0
      },
      message: this.formatFinalMessage(allMessages, null, "Workflow reached maximum iterations without completion")
    };
  }

  private async executeAgent(
    agentType: AgentType, 
    state: GraphState, 
    userInput: string,
    lastResponse: AgentResponse | null
  ): Promise<AgentResponse> {
    switch (agentType) {
      case AgentType.MANAGER:
        if (lastResponse?.error) {
          // Manager handling error from other agents
          return {
            agentType: AgentType.MANAGER,
            action: "error_handled",
            result: {
              message: `I encountered an issue: ${lastResponse.error}. Let me try a different approach.`,
              error: lastResponse.error
            },
            nextAgent: AgentType.PLANNER,
            shouldContinue: true
          };
        }
        
        // Check for help requests
        if (userInput.toLowerCase().includes("help")) {
          return await this.manager.handleHelp();
        }
        
        return await this.manager.processMessage(state, userInput);

      case AgentType.PLANNER:
        if (!state.currentTask) {
          throw new Error("No task available for planning");
        }
        
        if (lastResponse?.error) {
          // Planner refining plan due to error
          return await this.planner.refinePlan(state, state.currentTask, lastResponse.error);
        }
        
        return await this.planner.createPlan(state, state.currentTask);

      case AgentType.PROGRAMMER:
        if (!state.currentTask) {
          throw new Error("No task available for execution");
        }
        
        if (lastResponse?.error) {
          // Programmer handling error
          return await this.programmer.handleError(state, state.currentTask, lastResponse.error);
        }
        
        return await this.programmer.executeTask(state, state.currentTask);

      default:
        throw new Error(`Unknown agent type: ${agentType}`);
    }
  }

  private updateState(state: GraphState, response: AgentResponse): GraphState {
    const newState = { ...state };

    // Update messages
    if (response.result.message) {
      newState.messages = [
        ...state.messages,
        createMessage(response.result.message, "assistant")
      ];
    }

    // Update current task
    if (response.result.task) {
      newState.currentTask = response.result.task;
    }

    // Update context
    if (response.result.context) {
      newState.context = { ...state.context, ...response.result.context };
    }

    // Update last error
    if (response.error) {
      newState.lastError = response.error;
    } else {
      delete newState.lastError;
    }

    return newState;
  }

  private formatFinalMessage(messages: string[], finalResponse?: AgentResponse | null, errorMessage?: string): string {
    let result = "# Multi-Agent Execution Summary\n\n";
    
    if (errorMessage) {
      result += `⚠️ **Status**: ${errorMessage}\n\n`;
    } else if (finalResponse?.action === "task_completed") {
      result += "✅ **Status**: Task completed successfully!\n\n";
    } else {
      result += "ℹ️ **Status**: Processing completed\n\n";
    }
    
    result += "## Agent Interactions\n\n";
    messages.forEach((message, index) => {
      result += `${index + 1}. ${message}\n\n`;
    });
    
    if (finalResponse?.result.task) {
      const task = finalResponse.result.task;
      result += "## Task Summary\n\n";
      result += `**Title**: ${task.title}\n`;
      result += `**Status**: ${task.status}\n`;
      result += `**Plan Items**: ${task.planItems.length}\n`;
      
      if (task.planItems.length > 0) {
        result += "\n### Plan Items Status\n";
        task.planItems.forEach((item, index) => {
          const statusIcon = item.status === "completed" ? "✅" : 
                           item.status === "failed" ? "❌" : 
                           item.status === "in_progress" ? "🔄" : "⏳";
          result += `${index + 1}. ${statusIcon} ${item.description}\n`;
        });
      }
    }
    
    return result;
  }

  // Method to get current state for debugging
  getCurrentState(): Partial<GraphState> {
    return {
      workingDirectory: process.env.WORK_DIR || "./workspace",
      context: {},
      agentHistory: []
    };
  }
}
