import { ChatAnthropic } from "@langchain/anthropic";
import { ChatOpenAI } from "@langchain/openai";
import { BaseChatModel } from "@langchain/core/language_models/chat_models";
import { AgentConfig } from "../types/index.js";

export class LLMManager {
  private models: Map<string, BaseChatModel> = new Map();

  constructor(private config: AgentConfig) {}

  getModel(agentType?: string): BaseChatModel {
    const key = `${this.config.modelProvider}-${this.config.modelName}-${agentType || 'default'}`;
    
    if (!this.models.has(key)) {
      const model = this.createModel();
      this.models.set(key, model);
    }
    
    return this.models.get(key)!;
  }

  private createModel(): BaseChatModel {
    const baseConfig = {
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens || 4000,
    };

    switch (this.config.modelProvider) {
      case "anthropic":
        return new ChatAnthropic({
          modelName: this.config.modelName,
          ...baseConfig,
        });
      
      case "openai":
        return new ChatOpenAI({
          modelName: this.config.modelName,
          ...baseConfig,
        });
      
      default:
        throw new Error(`Unsupported model provider: ${this.config.modelProvider}`);
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const model = this.getModel();
      const response = await model.invoke([
        { role: "user", content: "Hello, please respond with 'OK' if you can hear me." }
      ]);
      return response.content.toString().includes("OK");
    } catch (error) {
      console.error("LLM connection test failed:", error);
      return false;
    }
  }
}

// Utility function to create LLM manager from environment
export function createLLMManager(): LLMManager {
  const config: AgentConfig = {
    modelProvider: (process.env.DEFAULT_MODEL_PROVIDER as "anthropic" | "openai") || "anthropic",
    modelName: process.env.DEFAULT_MODEL_NAME || "claude-3-5-sonnet-20241022",
    temperature: parseFloat(process.env.TEMPERATURE || "0"),
    maxTokens: parseInt(process.env.MAX_TOKENS || "4000"),
    workingDirectory: process.env.WORK_DIR || "./workspace",
    debug: process.env.DEBUG === "true"
  };

  return new LLMManager(config);
}
