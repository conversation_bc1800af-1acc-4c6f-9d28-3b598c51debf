import { v4 as uuidv4 } from "uuid";
import { Task, PlanItem, AgentType } from "../types/index.js";

export { LLMManager, createLLMManager } from "./llm.js";

// Utility functions for task and plan management
export function createTask(title: string, description: string): Task {
  return {
    id: uuidv4(),
    title,
    description,
    status: "pending",
    createdAt: Date.now(),
    updatedAt: Date.now(),
    planItems: [],
    currentPlanItemIndex: 0
  };
}

export function createPlanItem(description: string, dependencies?: string[]): PlanItem {
  return {
    id: uuidv4(),
    description,
    status: "pending",
    dependencies,
  };
}

export function updateTaskStatus(task: Task, status: Task["status"]): Task {
  return {
    ...task,
    status,
    updatedAt: Date.now()
  };
}

export function updatePlanItemStatus(task: Task, planItemId: string, status: PlanItem["status"], error?: string): Task {
  const planItems = task.planItems.map(item => 
    item.id === planItemId 
      ? { ...item, status, error, actualTime: status === "completed" ? Date.now() - (item.estimatedTime || 0) : undefined }
      : item
  );
  
  return {
    ...task,
    planItems,
    updatedAt: Date.now()
  };
}

export function getNextPlanItem(task: Task): PlanItem | null {
  const pendingItems = task.planItems.filter(item => item.status === "pending");
  
  // Find items with no dependencies or all dependencies completed
  const availableItems = pendingItems.filter(item => {
    if (!item.dependencies || item.dependencies.length === 0) {
      return true;
    }
    
    return item.dependencies.every(depId => {
      const depItem = task.planItems.find(p => p.id === depId);
      return depItem?.status === "completed";
    });
  });
  
  return availableItems[0] || null;
}

export function isTaskComplete(task: Task): boolean {
  return task.planItems.length > 0 && task.planItems.every(item => item.status === "completed");
}

export function hasTaskFailed(task: Task): boolean {
  return task.planItems.some(item => item.status === "failed");
}

// Logging utility
export function log(agentType: AgentType, message: string, data?: any): void {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [${agentType.toUpperCase()}]`;
  
  if (process.env.DEBUG === "true") {
    console.log(`${prefix} ${message}`, data ? JSON.stringify(data, null, 2) : "");
  } else {
    console.log(`${prefix} ${message}`);
  }
}

// Error handling utility
export function createError(agentType: AgentType, message: string, originalError?: Error): Error {
  const error = new Error(`[${agentType.toUpperCase()}] ${message}`);
  if (originalError) {
    error.stack = originalError.stack;
    error.cause = originalError;
  }
  return error;
}
