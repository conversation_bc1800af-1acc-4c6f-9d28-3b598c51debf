import dotenv from "dotenv";
import { SimpleMultiAgentCoordinator } from "./simple-coordinator.js";
import { ProcessRequest } from "./types/simple.js";

// Load environment variables
dotenv.config();

export class SimpleMultiAgentSystem {
  private coordinator: SimpleMultiAgentCoordinator;

  constructor(mockMode = true) {
    this.coordinator = new SimpleMultiAgentCoordinator(mockMode);
  }

  async processRequest(userInput: string, workingDirectory?: string): Promise<any> {
    const request: ProcessRequest = {
      userInput,
      workingDirectory: workingDirectory || process.env.WORK_DIR || "./workspace"
    };

    return await this.coordinator.processRequest(request);
  }

  async testConnection(): Promise<boolean> {
    // For the simple version, always return true
    return true;
  }
}

// CLI interface for direct usage
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log("🤖 Open SWE MVP - Multi-Agent System");
    console.log("=====================================");
    console.log("");
    console.log("Usage: npm run dev \"<your request>\"");
    console.log("");
    console.log("Examples:");
    console.log("  npm run dev \"Create a simple calculator function\"");
    console.log("  npm run dev \"Write a README file for this project\"");
    console.log("  npm run dev \"Build a basic web server\"");
    console.log("  npm run dev \"help\"");
    console.log("");
    process.exit(1);
  }

  const userInput = args.join(" ");
  console.log("🤖 Open SWE MVP - Multi-Agent System");
  console.log("=====================================");
  console.log(`📝 Processing request: "${userInput}"`);
  console.log("");

  try {
    const system = new SimpleMultiAgentSystem(true); // Mock mode enabled
    
    console.log("🔄 Starting multi-agent processing...");
    console.log("");
    
    const result = await system.processRequest(userInput);
    
    console.log("📊 EXECUTION RESULTS");
    console.log("=".repeat(50));
    console.log(`✅ Success: ${result.success}`);
    console.log(`📋 Task: ${result.task.title}`);
    console.log(`📈 Status: ${result.task.status}`);
    console.log(`⏱️  Execution Time: ${result.executionTime}ms`);
    console.log("");
    
    if (result.task.planItems.length > 0) {
      console.log(`📝 Plan Items (${result.task.planItems.length}):`);
      result.task.planItems.forEach((item, index) => {
        const statusIcon = item.status === "completed" ? "✅" : 
                          item.status === "failed" ? "❌" : 
                          item.status === "in_progress" ? "🔄" : "⏳";
        console.log(`   ${index + 1}. ${statusIcon} ${item.description}`);
      });
      console.log("");
    }

    console.log("📄 DETAILED RESULT:");
    console.log("-".repeat(50));
    console.log(result.finalResult);
    console.log("");

    if (result.error) {
      console.log("❌ ERROR:");
      console.log("-".repeat(50));
      console.log(result.error);
      console.log("");
    }

    // Show generated files if any
    if (result.success && result.task.status === "completed") {
      console.log("📁 Check the workspace directory for generated files:");
      console.log(`   ${process.env.WORK_DIR || "./workspace"}`);
      console.log("");
    }

    console.log("🎉 Processing completed!");

  } catch (error) {
    console.error("💥 Fatal error:", error);
    process.exit(1);
  }
}

// Export for library usage
export { SimpleMultiAgentCoordinator } from "./simple-coordinator.js";
export * from "./types/simple.js";
export * from "./agents/simple-manager.js";
export * from "./agents/simple-planner.js";
export * from "./agents/simple-programmer.js";
export * from "./tools/simple.js";
export * from "./utils/simple.js";

// Run CLI if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
